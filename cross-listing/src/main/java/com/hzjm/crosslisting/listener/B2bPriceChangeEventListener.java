package com.hzjm.crosslisting.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.listener.event.B2bPriceChangeEvent;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.repository.IPlatformListingService;
import com.hzjm.service.entity.SysUpdatePriceEvents;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.service.ISysUpdatePriceEventsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static cn.hutool.core.date.DateTime.now;

/**
 * <AUTHOR>
 * @date 2025/4/29 16:20
 * @description: B2bPriceChangeEvent 事件监听
 */
@Slf4j
@Component
public class B2bPriceChangeEventListener {
    @Resource
    private ISysUpdatePriceEventsService sysUpdatePriceEventsService;
    @Resource
    private IPlatformListingService platformListingService;

    /**
     * 监听 B2b 价格变更事件
     *
     * @param event 事件
     */
    @EventListener
    @Async
    public void handleB2bPriceChangeEvent(B2bPriceChangeEvent event) {
        String knetListingId = event.getKentListingId();
        LambdaQueryWrapper<PlatformListing> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .select(PlatformListing::getId)
                .eq(PlatformListing::getKnetListingId, knetListingId)
                .eq(PlatformListing::getPlatform, SourcePlatform.B2B_SHOP)
                .last("limit 1");
        int counted = platformListingService.count(queryWrapper);
        if (counted == 0) {
            // 如果商品不在b2b上架，则不需要处理
            log.info(" 商品不在b2b上架，knetListingId:{}", knetListingId);
            return;
        }
        SysUpdatePriceEvents entity = SysUpdatePriceEvents.builder()
                .knetListingId(knetListingId)
                .status(SysTaskStatus.PENDING)
                .createTime(now())
                .updateTime(now())
                .delFlag(0)
                .version(0)
                .build();
        try {
            sysUpdatePriceEventsService.save(entity);
        } catch (Exception e) {
            log.error("保存更新价格事件失败，knetListingId:{}，异常信息:{}", knetListingId, e.getMessage(), e);
        }
    }
}
