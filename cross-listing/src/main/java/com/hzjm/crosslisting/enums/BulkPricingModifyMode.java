package com.hzjm.crosslisting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BulkPricingModifyMode {
    QUICK_SALE("quick_sale"),
    PROFIT_CONTROL("profit_control"),
    EARN_MORE_PROFIT("earn_more_profit"),
    SMART_PRICING("smart_pricing");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }
}
