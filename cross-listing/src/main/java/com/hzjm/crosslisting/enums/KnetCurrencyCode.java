package com.hzjm.crosslisting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.hzjm.stockx.data.StockXCurrencyCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum KnetCurrencyCode {

    CNY("CNY"),
    AUD("AUD"),
    CAD("CAD"),
    CHF("CHF"),
    EUR("EUR"),
    GBP("GBP"),
    HKD("HKD"),
    JPY("JPY"),
    KRW("KRW"),
    MXN("MXN"),
    NZD("NZD"),
    SGD("SGD"),
    USD("USD");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }

    // 从StockX 创建 货币代码
    public static KnetCurrencyCode createFrom(StockXCurrencyCode currencyCode) {
        switch (currencyCode) {
            case AUD: return KnetCurrencyCode.AUD;
            case CAD: return KnetCurrencyCode.CAD;
            case CHF: return KnetCurrencyCode.CHF;
            case EUR: return KnetCurrencyCode.EUR;
            case GBP: return KnetCurrencyCode.GBP;
            case HKD: return KnetCurrencyCode.HKD;
            case JPY: return KnetCurrencyCode.JPY;
            case KRW: return KnetCurrencyCode.KRW;
            case MXN: return KnetCurrencyCode.MXN;
            case NZD: return KnetCurrencyCode.NZD;
            case SGD: return KnetCurrencyCode.SGD;
            case USD: return KnetCurrencyCode.USD;
            default: throw new IllegalArgumentException("Create KnetCurrencyCode Error, Unsupported currency code:" + currencyCode);
        }
    }

    public StockXCurrencyCode toStockXCurrencyCode() {
        switch (this) {
            case AUD: return StockXCurrencyCode.AUD;
            case CAD: return StockXCurrencyCode.CAD;
            case CHF: return StockXCurrencyCode.CHF;
            case EUR: return StockXCurrencyCode.EUR;
            case GBP: return StockXCurrencyCode.GBP;
            case HKD: return StockXCurrencyCode.HKD;
            case JPY: return StockXCurrencyCode.JPY;
            case KRW: return StockXCurrencyCode.KRW;
            case MXN: return StockXCurrencyCode.MXN;
            case NZD: return StockXCurrencyCode.NZD;
            case SGD: return StockXCurrencyCode.SGD;
            case USD: return StockXCurrencyCode.USD;
            default: throw new IllegalArgumentException("Create KnetCurrencyCode Error, Unsupported currency code:" + this);
        }
    }

    // 从 生字符串 创建 货币代码
    public static KnetCurrencyCode createFrom(String rawCurrencyCode) {
        String upperCaseCurrencyCode = rawCurrencyCode.toUpperCase();
        switch (upperCaseCurrencyCode) {
            case "USD":
                return KnetCurrencyCode.USD;
            case "SGD":
                return KnetCurrencyCode.SGD;
            case "NZD":
                return KnetCurrencyCode.NZD;
            case "MXN":
                return KnetCurrencyCode.MXN;
            case "KRW":
                return KnetCurrencyCode.KRW;
            case "JPY":
                return KnetCurrencyCode.JPY;
            case "HKD":
                return KnetCurrencyCode.HKD;
            case "GBP":
                return KnetCurrencyCode.GBP;
            case "EUR":
                return KnetCurrencyCode.EUR;
            case "CHF":
                return KnetCurrencyCode.CHF;
            case "CAD":
                return KnetCurrencyCode.CAD;
            case "CNY":
                return KnetCurrencyCode.CNY;
            default:
                throw new IllegalArgumentException("Create KnetCurrencyCode Error, Unsupported currency code:" + rawCurrencyCode);
        }
    }
}
