package com.hzjm.crosslisting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SaleType {

    CONSIGNMENT("CONSIGNMENT"), // 寄售
    SALE("SALE"); // 挂售

    @EnumValue
    @JsonValue
    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
