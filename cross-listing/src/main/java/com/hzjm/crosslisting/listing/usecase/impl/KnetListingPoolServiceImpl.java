package com.hzjm.crosslisting.listing.usecase.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.crosslisting.enums.*;
import com.hzjm.crosslisting.listener.event.B2bPriceChangeEvent;
import com.hzjm.crosslisting.listing.data.dto.NotifyKnetListingInfoDto;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingResult;
import com.hzjm.crosslisting.listing.entity.AutoPricingSettings;
import com.hzjm.crosslisting.listing.entity.KnetProductListing;
import com.hzjm.crosslisting.listing.entity.ListingActionItem;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.repository.IKnetProductListingService;
import com.hzjm.crosslisting.listing.repository.IListingPricingHistoryService;
import com.hzjm.crosslisting.listing.repository.IPlatformListingService;
import com.hzjm.crosslisting.listing.usecase.IAutoPricingStrategyService;
import com.hzjm.crosslisting.listing.usecase.IKnetListingPoolService;
import com.hzjm.crosslisting.listing.usecase.IListingAccountAdapter;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.repository.IPlatformOrderService;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import com.hzjm.crosslisting.product.repository.IKnetProductService;
import com.hzjm.service.service.ISysProdSearchService;
import com.hzjm.service.service.ISysProdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KnetListingPoolServiceImpl implements IKnetListingPoolService {

    @Autowired
    IKnetProductListingService knetProductListingService;

    @Autowired
    IKnetProductService knetProductService;

    @Autowired
    IListingPricingHistoryService listingPricingHistoryService;

    @Autowired
    IListingAccountAdapter listingAccountAdapter;

    @Autowired
    IPlatformListingService platformListingService;

    @Autowired
    IPlatformOrderService platformOrderService;

    @Autowired
    ISysProdService sysProdService;

    @Autowired
    ISysProdSearchService sysProdSearchService;

    @Autowired
    IAutoPricingStrategyService autoPricingStrategyService;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 从 寄售商品池里 拉取待更新的寄售商品
     *
     * @return
     */
    @Override
    public List<ListingActionItem> fetchListingMission(boolean shouldSetUpdating, KnetListingPendingAction pendingAction) {
        List<ListingActionItem> pendingUpdateItems = Collections.synchronizedList(new ArrayList<>());

        // 取 Auto Pricing 处于打开状态 或 处于 Pending Update 状态的 数据
        List<KnetProductListing> allPendingActionListing = fetchKnetProductListingWith(pendingAction);

        // 为空直接返回
        if (allPendingActionListing.isEmpty()) {
            log.error("No" + pendingAction + "Knet Listing mission to process.");
            return pendingUpdateItems;
        }

        // 循环所有平台
        Arrays.stream(SourcePlatform.values()).forEach(platform -> {
            allPendingActionListing
                    .stream()
                    // 去除不必要的 listing 数据
                    .filter(knetListing -> {
                        // 非 待更新 与 Auto Pricing 的任务, 只需判断是否任务类型 与 设置的相等即可
                        switch (pendingAction) {
                            case PENDING_CREATE:
                                // 待创建的类型判断是否能创建
                                return knetListing.canCreate(platform);
                            case PENDING_UPDATE:
                                // 如果对应平台的这个数据的 当前售价是不合法的，也直接过滤，没有比较的对象
                                if (knetListing.salePriceIsInvalid(platform)) {
                                    knetListing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                                    return false;
                                }

                                // 如果修改的价格 与上次相同也不会改价
                                if (autoPricingStrategyService.biddingIsSameAsLastPrice(knetListing, platform)) {
                                    knetListing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                                    return false;
                                }

                                if (!knetListing.canUpdate(platform)) {
                                    knetListing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                                    return false;
                                }

                                // 返回是否能更新的结果
                                return true;

                            case PENDING_DELETE:
                            case PENDING_DEACTIVATE:
                                // 待下架 和待删除的任务类型不做任何拦截不过滤
                                return knetListing.canDeactivate(platform);
                            case PENDING_AUTO_PRICING:
                                // 如果对应平台的这个数据的 当前售价是不合法的，也直接过滤，没有比较的对象
                                if (knetListing.salePriceIsInvalid(platform)) {
                                    return false;
                                }

                                // 如果当前平台对应的 auto pricing 是不可用的 直接过滤掉
                                if (knetListing.shouldNotAutoPricing(platform)) {
                                    return false;
                                }

                                //根据策略 判断是否该进行 auto pricing
                                if (!autoPricingStrategyService.calculateAutoPricingIsEffective(knetListing, platform)) {
                                    return false;
                                }

                                // 处于Pending Update 或 Auto Pricing 处于打开状态的订单 且其 Listing Id 不能为空且其当前售价与当前市场最低价不相同
                                return knetListing.canUpdate(platform);

                            default:
                                // 非定义的 任务类型直接过滤
                                return false;
                        }
                    })
                    // 循环组装创建任务的数据
                    .forEach(filteredListing -> {
                        try {
                            // 如果是创建 类型则需要取计算 上架到哪个 账号
                            // 如果非 pending create 的 类型 则不需要计算 哪个账号
                            if (Objects.equals(pendingAction, KnetListingPendingAction.PENDING_CREATE)) {
                                ListingAccount account = listingAccountAdapter.createAccountBy(filteredListing, platform);
                                pendingUpdateItems.add(filteredListing.createListingItem(platform, account, true));
                            } else {
                                pendingUpdateItems.add(filteredListing.createListingItem(platform, null, !Objects.equals(pendingAction, KnetListingPendingAction.PENDING_AUTO_PRICING)));
                            }
                            if (shouldSetUpdating) {
                                switch (platform) {
                                    case STOCK_X:
                                        filteredListing.setStockxPendingAction(pendingAction.actionResult());
                                        break;
                                    case GOAT_STV:
                                        filteredListing.setGoatPendingAction(pendingAction.actionResult());
                                        break;
                                    case GOAT_INSTANT_SHIP:
                                        filteredListing.setGoatisPendingAction(pendingAction.actionResult());
                                        break;
                                    case KICKS_CREW:
                                        filteredListing.setKcPendingAction(pendingAction.actionResult());
                                        break;
                                    case EBAY:
                                        filteredListing.setEbayPendingAction(pendingAction.actionResult());
                                        break;
                                    case POIZON:
                                        filteredListing.setPoizonPendingAction(pendingAction.actionResult());
                                        break;
                                    case TIKTOK_SHOP:
                                        filteredListing.setTtsPendingAction(pendingAction.actionResult());
                                        break;
                                    case B2B_SHOP:
                                        filteredListing.setB2bPendingAction(pendingAction.actionResult());
                                        break;
                                    default:
                                        break;
                                }
                            }
                        } catch (Exception e) {
                            String errorInfo = "Update" + platform + "ListingActionItem error, one id: " + filteredListing.getOneId()
                                    + ", error message: " + e
                                    + ", stack Trace: " + BaseUtils.getCallStack();
                            log.error(errorInfo);
                        }

                    });

        });

        knetProductListingService.saveOrUpdateBatch(allPendingActionListing);

        return pendingUpdateItems;
    }

    // 根据执行任务类型获取数据库中需要被执行对应任务的 knet product listing 数据
    private List<KnetProductListing> fetchKnetProductListingWith(KnetListingPendingAction action) {
        LambdaQueryWrapper<KnetProductListing> query = Wrappers.<KnetProductListing>lambdaQuery();
        // 根据不同的操作类型来返回对应的数据
        switch (action) {
            case PENDING_CREATE:
            case PENDING_UPDATE:
                query.in(KnetProductListing::getKnetListingStatus,
                                KnetProductListingStatus.CREATED,
                                KnetProductListingStatus.UPDATED)
                        .and(wrapper -> wrapper
                                .eq(KnetProductListing::getStockxPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getGoatPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getGoatisPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getKcPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getEbayPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getPoizonPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getTtsPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getB2bPendingAction, action)
                        );
                break;
            case PENDING_DELETE:
            case PENDING_DEACTIVATE:
                query.notIn(KnetProductListing::getKnetListingStatus,
                                KnetProductListingStatus.COMPLETED,
                                KnetProductListingStatus.DELETED,
                                KnetProductListingStatus.MATCHED)
                        .and(wrapper -> wrapper
                                .eq(KnetProductListing::getStockxPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getGoatPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getGoatisPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getKcPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getEbayPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getPoizonPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getTtsPendingAction, action)
                                .or()
                                .eq(KnetProductListing::getB2bPendingAction, action)
                        );
                break;
            case PENDING_AUTO_PRICING:
                query.in(KnetProductListing::getKnetListingStatus,
                                KnetProductListingStatus.CREATED,
                                KnetProductListingStatus.UPDATED
                        )
                        .and(wrapper -> {
                            wrapper.eq(KnetProductListing::getStockxAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getGoatAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getGoatisAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getKcAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getEbayAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getPoizonAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getTtsAutoPricingStatus, PlatformAutoPricingStatus.OPEN)
                                    .or()
                                    .eq(KnetProductListing::getB2bAutoPricingStatus, PlatformAutoPricingStatus.OPEN);
                        });
                break;
            default:
                throw new BaseException("unsupported action to fetch knet product listing to generate ListingActionItem.");
        }

        List<KnetProductListing> pendingActionListings = knetProductListingService.list(query);

        // 为空直接返回
        if (pendingActionListings.isEmpty()) {
            log.info("No " + action + " Knet Listing mission to process.");
            return pendingActionListings;
        }

        //  当为更新或者自动跟价的任务的时候 需要再去查一下 platform order 是否有成交的订单， 有就不会去更新
        if (Objects.equals(action, KnetListingPendingAction.PENDING_UPDATE)
                || Objects.equals(action, KnetListingPendingAction.PENDING_AUTO_PRICING)) {

            List<String> oneIds = pendingActionListings.stream()
                    .map(KnetProductListing::getOneId)
                    .collect(Collectors.toList());

            //  有需要被修改的数据, 取消的order 除外
            List<PlatformOrder> orders = platformOrderService.list(
                    Wrappers.<PlatformOrder>lambdaQuery()
                            .in(PlatformOrder::getOneId, oneIds)
                            .notIn(PlatformOrder::getKnetOrderStatus, PlatformOrderStatus.CANCELED)
            );

            // 不为空
            if (!orders.isEmpty()) {
                // 已经有订单匹配的 oneId
                List<String> hasOrderOneIds = orders.stream().map(PlatformOrder::getOneId).collect(Collectors.toList());

                pendingActionListings = pendingActionListings
                        .stream()
                        .filter(kpl -> !hasOrderOneIds.contains(kpl.getOneId()))
                        .collect(Collectors.toList());
            }
        }

        return pendingActionListings;
    }

    /**
     * @param needUpdateListingDto
     * @return
     */
    @Override
    public boolean updateListingInfo(NotifyKnetListingInfoDto needUpdateListingDto) {
        for (PlatformListingResult pullResult : needUpdateListingDto.getPullResult()) {
            try {
                handleNeedUpdatePlatformListing(pullResult);
            } catch (Exception e) {
                String errorInfo = "返回批量操作结果时，更新 knet Product listing 失败, message: "
                        + e
                        + ", pullResultAction " + pullResult.getPendingAction()
                        + ", sku: " + pullResult.getSku()
                        + ", size: " + pullResult.getSize()
                        + ", stack trace: " + BaseUtils.getCallStack();
                log.error(errorInfo);
            }
        }
        return true;
    }

    private void handleNeedUpdatePlatformListing(PlatformListingResult pullResult) {
        // 根据  Knet Listing Id 去重
        Map<String, List<PlatformListing>> platformListingsMap = pullResult
                .getUpdatedListings()
                .stream()
                .filter(pl -> pl.getKnetListingId() != null)
                .collect(Collectors.groupingBy(PlatformListing::getKnetListingId));

        if (platformListingsMap.isEmpty()) {
            log.info("Empty platform listings to update knet product listing, return.");
            return;
        }

        // 需要更新的 Knet Listing 条目
        List<KnetProductListing> needUpdateKnetListings = knetProductListingService.list(
                Wrappers.<KnetProductListing>lambdaQuery()
                        .in(KnetProductListing::getKnetListingId, platformListingsMap.keySet())
                        .ne(Objects.equals(pullResult.getPendingAction(), PendingAction.MATCHED), KnetProductListing::getKnetListingStatus, KnetProductListingStatus.MATCHED)
        );

        if (needUpdateKnetListings.isEmpty()) {
            log.info("Need Update Knet Listing info is empty");
            return;
        }

        //log.info("pull result 的 回执任务类型为: " + pullResult.getPendingAction() + ", 总共取出的结果有: " + needUpdateKnetListings.toString());

        for (KnetProductListing listing : needUpdateKnetListings) {
            // 如果存在有需要更新的StockX Platform Listing，更新对应的 Knet Listing
            Arrays.stream(SourcePlatform.values()).forEach(platform -> {
                //b2b 平台价格计算事件 标识
                boolean b2bPriceChange = false;
                PlatformListing selectedPlatformListing = platformListingsMap
                        .get(listing.getKnetListingId())
                        .stream()
                        .filter(pl -> Objects.equals(pl.getPlatform(), platform))
                        .findFirst()
                        .orElse(null);

                if (ObjectUtils.isEmpty(selectedPlatformListing)) {
                    return;
                }

                listing.setUpdatedAt(selectedPlatformListing.getGmtModify());
                switch (pullResult.getPendingAction()) {
                    case CREATE_NEED_CONFIRM:
                        if (Objects.equals(selectedPlatformListing.getPendingAction(), PendingAction.CREATE_FAILED)) {
                            listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                            listing.updatePlatformStatus(platform, PlatformStatus.CLOSE);
                            //listing.setPoizonListingId(selectedPlatformListing.getPlatformListingId());
                        }
                        break;
                    case UPDATE_NEED_CONFIRM:
                        if (Objects.equals(selectedPlatformListing.getPendingAction(), PendingAction.UPDATE_FAILED)) {
                            listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                        }
                        break;
                    case DEACTIVATE_NEED_CONFIRM:
                    case DELETE_NEED_CONFIRM:
                        if (Objects.equals(selectedPlatformListing.getPendingAction(), PendingAction.DEACTIVATE_FAILED)) {
                            // 下架失败就再次设置任务状态
                            listing.updatePlatformPendingAction(platform, KnetListingPendingAction.PENDING_DEACTIVATE);
                        }
                        break;
                    case CREATED:
                    case UPDATE_FAILED:
                    case INFO_UPDATED:  // 已改价更新同步的数据
                        // 确定价格
                        Integer modifyPrice = (Objects.equals(platform, SourcePlatform.POIZON) ||
                                Objects.equals(platform, SourcePlatform.TIKTOK_SHOP))
                                ? selectedPlatformListing.getListingPrice()
                                : selectedPlatformListing.getSalePrice();
                        listing.updatePlatformSalePrice(platform, modifyPrice);
                        listing.updatePlatformListingId(platform, selectedPlatformListing.getPlatformListingId());
                        listing.updatePlatformListingStatus(platform, selectedPlatformListing.getSaleStatus());
                        // 对于 CREATED 状态，处理成功情况
                        if (pullResult.getPendingAction() == PendingAction.CREATED) {
                            boolean createdSuccess = !Objects.equals(selectedPlatformListing.pendingAction, PendingAction.CREATE_FAILED);
                            listing.updatePlatformStatus(platform, createdSuccess ? PlatformStatus.OPEN : PlatformStatus.CLOSE);
                            listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                            if (createdSuccess) {
                                listingPricingHistoryService.saveWithCheck(selectedPlatformListing, true);
                            }
                        } else {
                            // 对于其他状态（UPDATE_FAILED 和 INFO_UPDATED），将状态设置为 OPEN
                            listing.updatePlatformStatus(platform, PlatformStatus.OPEN);
                            if (listing.hasNotActionToExecute(platform)) {
                                listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                            }
                            listingPricingHistoryService.saveWithCheck(selectedPlatformListing, !listing.autoPricingEnabled(platform));
                        }
                        // 非b2b 触发价格变更事件
                        b2bPriceChange = !Objects.equals(platform, SourcePlatform.B2B_SHOP);
                        break;
                    case DEACTIVATED:
                        // 先定义一个标识变量
                        boolean shouldExecute = !(Objects.equals(platform, SourcePlatform.STOCK_X)
                                && Objects.equals(selectedPlatformListing.getPendingAction(), PendingAction.DEACTIVATE_FAILED));
                        if (shouldExecute) {
                            listing.updatePlatformListingId(platform, "");
                            listing.updatePlatformStatus(platform, PlatformStatus.CLOSE);
                            listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                            listing.updatePlatformListingStatus(platform, ListingStatus.INACTIVE);

                            // 设置自动定价
                            AutoPricingSettings settings = new AutoPricingSettings();
                            settings.setPlatform(platform);
                            settings.setAutoPricingMode(AutoPricingMode.EQUAL_MODE);
                            settings.setAutoPricingEnable(false);
                            settings.setBottomLine(new KnetProductPrice(0));
                            listing.updatePlatformAutoPricingSettings(settings);
                            b2bPriceChange = !Objects.equals(platform, SourcePlatform.B2B_SHOP);
                            // 设置完成全平台关闭的话
                            if (listing.allPlatformShouldClose()) {
                                b2bPriceChange = false;
                                listing.setKnetListingStatus(KnetProductListingStatus.DELETED);
                            }
                        }
                        break;
                    case MATCHED:
                    case COMPLETED:
                        // 有成交就设置成 整个 Knet Listing 为 MATCHED
                        Integer matchedPrice = (Objects.equals(platform, SourcePlatform.POIZON) ||
                                Objects.equals(platform, SourcePlatform.TIKTOK_SHOP))
                                ? selectedPlatformListing.getListingPrice()
                                : selectedPlatformListing.getSalePrice();
                        listing.updatePlatformSalePrice(platform, matchedPrice);

                        KnetProductListingStatus knetProductListingStatus = pullResult.getPendingAction() == PendingAction.MATCHED
                                ? KnetProductListingStatus.MATCHED
                                : KnetProductListingStatus.COMPLETED;
                        listing.setKnetListingStatus(knetProductListingStatus);
                        listing.updatePlatformListingStatus(platform, selectedPlatformListing.getSaleStatus());
                        listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                        // 全平台的 Auto Pricing 关闭
                        listing.closeAllAutoPricing();
                        break;
                    case CANCELED:
                        listing.updatePlatformStatus(platform, PlatformStatus.DISABLED);
                        listing.updatePlatformListingId(platform, "");
                        listing.updatePlatformPendingAction(platform, KnetListingPendingAction.INFO_UPDATED);
                        listing.updatePlatformListingStatus(platform, ListingStatus.CANCELED);
                        // 设置自动定价关闭
                        AutoPricingSettings settings = new AutoPricingSettings();
                        settings.setPlatform(platform);
                        settings.setAutoPricingMode(AutoPricingMode.EQUAL_MODE);
                        settings.setAutoPricingEnable(false);
                        settings.setBottomLine(new KnetProductPrice(0));
                        listing.updatePlatformAutoPricingSettings(settings);
                        break;
                    default:
                        break;
                }
                // 发送b2b重新计算价格事件
                if (b2bPriceChange) {
                    // 发送b2b重新计算价格事件
                    try {
                        B2bPriceChangeEvent orderCreatedEvent = new B2bPriceChangeEvent(this, listing.getKnetListingId());
                        eventPublisher.publishEvent(orderCreatedEvent);
                    }catch (Exception e) {
                        log.error("发送b2b重新计算价格事件失败，knetListingId: " + listing.getKnetListingId() + ", message: " + e);
                    }
                }
            });

        }

        //log.info("post_update_info: " + pullResult.getPendingAction() + " 类型 更新 knet product listing 数据: {}", needUpdateKnetListings.toString());

        if (!needUpdateKnetListings.isEmpty()) {
            // 如果是订单进来的 代码 一个个提交
            if (Objects.equals(pullResult.getPendingAction(), PendingAction.MATCHED)) {
                needUpdateKnetListings.forEach(updateListing -> {
                    try {
                        boolean isSuccess = knetProductListingService.updateById(updateListing);
                        // 更新数据库
                        //log.info("post_update_info: " + pullResult.getPendingAction() + " 类型 挨个更新 knet product listing 数据: " + updateListing + ", 结果: " + isSuccess);
                    } catch (Exception e) {
                        // 更新数据库
                        log.error("post_update_info: " + pullResult.getPendingAction() + " 类型 挨个更新 knet product listing 数据 失败: " + updateListing + ", message: " + e);
                    }
                });
            } else if (Objects.equals(pullResult.getPendingAction(), PendingAction.DEACTIVATED)) {
                // 按 是否是完全下架 的 KnetProductListing 切分
                Map<Boolean, List<KnetProductListing>> partitioned = needUpdateKnetListings.stream()
                        .collect(Collectors.partitioningBy(kpl -> Objects.equals(kpl.getKnetListingStatus(), KnetProductListingStatus.DELETED)));

                // partitioned.get(false) 是 knetListingStatus 不为 "DELETED" 的列表 这里只更新数据就行
                List<KnetProductListing> notDeletedList = partitioned.get(false);
                if (!notDeletedList.isEmpty()) {
                    knetProductListingService.saveOrUpdateBatch(notDeletedList);
                }

                // partitioned.get(true) 是 knetListingStatus 为 "DELETED" 的列表， 除了更新kentProductListing 还需要同时将 SysProd 置为空闲
                List<KnetProductListing> deletedList = partitioned.get(true);
                if (!deletedList.isEmpty()) {
                    knetProductListingService.deactivateAndReleaseSysProd(deletedList);
                }

            } else {
                knetProductListingService.saveOrUpdateBatch(needUpdateKnetListings);
            }

        }

    }

}
