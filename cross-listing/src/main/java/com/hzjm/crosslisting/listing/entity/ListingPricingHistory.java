package com.hzjm.crosslisting.listing.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.crosslisting.enums.AutoPricingMode;
import com.hzjm.crosslisting.enums.ListingPricingModifyType;
import com.hzjm.crosslisting.enums.SourcePlatform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ListingPricingHistory", description="Listing 改价历史")
public class ListingPricingHistory extends Model<ListingPricingHistory> implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    public Integer id;

    @ApiModelProperty(value = "表单创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    public Date gmtCreate;

    @ApiModelProperty(value = "上次更新时间")
    @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
    public Date gmtModify;

    @ApiModelProperty(value = "逻辑删除")
    @TableLogic
    public Integer delFlag = 0;

    @ApiModelProperty("Knet Product Listing Id")
    public String knetListingId;

    @ApiModelProperty("Platform Listing Id")
    public String listingId;

    @ApiModelProperty("Sale Price")
    public int salePrice;

    @ApiModelProperty("改价人类型")
    public ListingPricingModifyType modifyType;

    @ApiModelProperty("商户Id")
    public String shopUserId;

    @ApiModelProperty("商品 One Id")
    public String oneId;

    @ApiModelProperty("自动跟价的 模式")
    public AutoPricingMode autoPricingMode;

    @ApiModelProperty("所属平台")
    public SourcePlatform platform;

    @ApiModelProperty("设置改价历史收否过期")
    public boolean expired;

    // 从price
    public static ListingPricingHistory createFrom(PlatformListing listing, Boolean modifyByUser) {
        ListingPricingHistory pricingHistory = new ListingPricingHistory();
        pricingHistory.setGmtCreate(DateTimeUtils.getNow());
        pricingHistory.setGmtModify(DateTimeUtils.getNow());
        pricingHistory.setListingId(listing.getPlatformListingId());
        pricingHistory.setKnetListingId(listing.getKnetListingId());
        pricingHistory.setSalePrice(listing.getSalePrice());
        if (Objects.equals(listing.getPlatform(), SourcePlatform.POIZON) || Objects.equals(listing.getPlatform(), SourcePlatform.TIKTOK_SHOP)) {
            pricingHistory.setSalePrice(listing.getListingPrice());
        }else {
            pricingHistory.setSalePrice(listing.getSalePrice());
        }
        pricingHistory.setPlatform(listing.getPlatform());
        pricingHistory.setShopUserId(listing.getShopUserId());
        pricingHistory.setOneId(listing.getOneId());
        pricingHistory.setAutoPricingMode(listing.getAutoPricingMode());
        pricingHistory.setModifyType(modifyByUser ? ListingPricingModifyType.USER : ListingPricingModifyType.AUTO_PRICING);
        return  pricingHistory;
    }

}
