package com.hzjm.crosslisting.listing.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingPageDto;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingQueryDto;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingSearchDto;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingListVo;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingQueryVo;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingVo;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.entity.PlatformListingFlexCount;

import java.util.List;


/**
 * 平台真正的寄售单 服务类
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
public interface IPlatformListingService extends IService<PlatformListing> {

    PlatformListing getByIdWithoutLogic(Integer id);

    PlatformListingVo getDetail(Integer id);

    Boolean savePlatformListing(PlatformListing dto);

    Boolean insertList(List<PlatformListing> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<PlatformListingListVo> searchList(PlatformListingPageDto dto);

    List<PlatformListing> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<PlatformListing> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    int insertOrUpdateBatch(List<PlatformListing> platformListings);

    int updatePlatformListingId(String originListingId, String newListingId);

    IPage<PlatformListingVo> searchList(PlatformListingSearchDto dto);

    PlatformListingFlexCount selectFlexCount(PlatformListingSearchDto dto);

    /**
     * 查询所有 KnetProductListing 已经设置为下架 但是 三方listing 没有及时下架的数据
     *
     * @return 符合条件的 PlatformListing 列表
     */
    List<PlatformListing> searchNotDeactivatePlatformListings();

    List<String> getPendingManageListingTemplateIds();

    /**
     * 只更新 lisitng 的 销售价格 以及 销售状态，且 MATCHED 不会被更新
     *
     * @param platformListings
     */
    void updateListingPriceAndStatus(List<PlatformListing> platformListings);


    List<PlatformListing> selectNeedManageActivateListings();

    /**
     * 查询市场数据
     *
     * @param knetListingIds knetListingId 列表
     * @return 符合条件的 市场数据
     */
    List<PlatformListing> queryMarketDataByKnetListings(List<String> knetListingIds);

    /**
     * 根据条件查询平台寄售单列表（支持分页）
     *
     * @param dto 查询条件
     * @return 分页查询结果
     */
    IPage<PlatformListingQueryVo> queryPlatformListings(PlatformListingQueryDto dto);
}
