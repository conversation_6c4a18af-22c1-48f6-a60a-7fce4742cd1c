package com.hzjm.crosslisting.listing.data.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台寄售单查询响应VO
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
@ApiModel(value = "PlatformListingQueryVo", description = "平台寄售单查询响应")
public class PlatformListingQueryVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Integer id;

    @ApiModelProperty(value = "表单创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "上次更新时间")
    private Date gmtModify;

    @ApiModelProperty(value = "Knet 平台的 ListingId")
    private String knetListingId;

    @ApiModelProperty(value = "商户Id")
    private String shopUserId;

    @ApiModelProperty(value = "one Id")
    private String oneId;

    @ApiModelProperty(value = "对应平台上的真实的寄售单Id")
    private String platformListingId;

    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "创建订单时的操作ID，它可能对应了多笔寄售订单")
    private String batchId;

    @ApiModelProperty(value = "用户 listing 出价，并非真实的 listing 价格")
    private Integer listingPrice;

    @ApiModelProperty(value = "寄售商品的出价，美分单位")
    private Integer salePrice;

    @ApiModelProperty(value = "平台的Listing状态")
    private String saleStatus;

    @ApiModelProperty(value = "商品 模板Id")
    private String templateId;

    @ApiModelProperty(value = "商品 变体Id")
    private String variantId;

    @ApiModelProperty(value = "寄售商品属性对应的Knet平台Id")
    private String knetId;

    @ApiModelProperty(value = "是否打开 AutoPricing")
    private Boolean autoPricingEnable;

    @ApiModelProperty(value = "底线价")
    private Integer bottomLine;

    @ApiModelProperty(value = "所属的账号")
    private String account;

    @ApiModelProperty(value = "寄售单所属平台")
    private String platform;
}
