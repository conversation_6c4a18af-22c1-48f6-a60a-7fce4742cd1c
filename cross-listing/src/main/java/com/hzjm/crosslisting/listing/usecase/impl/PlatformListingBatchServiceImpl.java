package com.hzjm.crosslisting.listing.usecase.impl;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.crosslisting.enums.PendingAction;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.listing.data.dao.PlatformListingMapper;
import com.hzjm.crosslisting.listing.data.dto.B2bUpdatePriceDto;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingResult;
import com.hzjm.crosslisting.listing.entity.ListingActionItem;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.repository.IListingRepository;
import com.hzjm.crosslisting.listing.usecase.IPlatformListingBatchService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PlatformListingBatchServiceImpl implements IPlatformListingBatchService {

    @Qualifier("listingRepositoryStockXImpl")
    @Autowired
    IListingRepository stockXListingUseCase;

    @Qualifier("listingRepositoryGoatImpl")
    @Autowired
    IListingRepository goatListingUseCase;

    @Qualifier("listingRepositoryKicksCrewImpl")
    @Autowired
    IListingRepository kicksCrewListingUseCase;

    @Qualifier("listingRepositoryEbayImpl")
    @Autowired
    IListingRepository ebayListingUseCase;

    @Qualifier("listingRepositoryPoizonVirtualImpl")
    @Autowired
    IListingRepository poizonListingUseCase;

    @Qualifier("listingRepositoryTTSImpl")
    @Autowired
    IListingRepository ttsListingUseCase;

    @Qualifier("listingRepositoryB2bImpl")
    @Autowired
    IListingRepository b2bListingUseCase;

    @Resource
    private PlatformListingMapper platformListingMapper;

    /**
     * @param requestList
     * @param pendingAction
     * @return
     */
    @Override
    public List<PlatformListingResult> batchListing(List<ListingActionItem> requestList, PendingAction pendingAction) {
        // 批量创建任务的结果数组
        List<PlatformListingResult> batchMissionResultLists = new ArrayList<>();

        // Kicks Crew 和 goat StockX 不同 在 批次创建后我们可以直接获取结果，因此我们将 KC 的 结果单个封装。

        // 1. async batch mission result
        PlatformListingResult batchMissionResult = new PlatformListingResult();
        batchMissionResult.setPendingAction(pendingAction.toMissionAsyncResult());

        // 2. sync batch mission result
        PlatformListingResult syncKcMissionResult = new PlatformListingResult();
        syncKcMissionResult.setPendingAction(pendingAction.toMissionSyncResult());

        //3. sync batch poizon mission result
        PlatformListingResult syncPoizonMissionResult = new PlatformListingResult();
        syncPoizonMissionResult.setPendingAction(pendingAction.toMissionSyncResult());

        //4. sync batch tts mission result
        PlatformListingResult syncTtsMissionResult = new PlatformListingResult();
        syncTtsMissionResult.setPendingAction(pendingAction.toMissionSyncResult());

        //5. sync batch b2b mission result
        PlatformListingResult syncB2bMissionResult = new PlatformListingResult();
        syncB2bMissionResult.setPendingAction(pendingAction.toMissionSyncResult());

        List<PlatformListing> batchListings = new ArrayList<>();
        switch (pendingAction) {
            case PENDING_CREATE:
                batchListings = batchCreateFromKnetListingPool(requestList);
                break;
            case PENDING_UPDATE:
                batchListings = batchUpdateFromKnetListingPool(requestList);
                break;
            case PENDING_DEACTIVATE:
                batchListings = batchDeactivateFromKnetListingPool(requestList);
                break;
            default:
                throw new BaseException("unsupported " + pendingAction + " to execute batch listing mission.");
        }

        // 对返回的结果 拆分成 kc, ebay, 和其他 平台不能立即返回结果的 listing
        Map<SourcePlatform, List<PlatformListing>> grouped = batchListings.stream()
                .collect(Collectors.groupingBy(PlatformListing::getPlatform));

        List<PlatformListing> kicksCrewListings = grouped.getOrDefault(SourcePlatform.KICKS_CREW, Collections.emptyList());
        List<PlatformListing> poizonListings = grouped.getOrDefault(SourcePlatform.POIZON, Collections.emptyList());
        List<PlatformListing> ttsListing = grouped.getOrDefault(SourcePlatform.TIKTOK_SHOP, Collections.emptyList());
        List<PlatformListing> b2bListing = grouped.getOrDefault(SourcePlatform.B2B_SHOP, Collections.emptyList());
        List<PlatformListing> otherPlatformListings = grouped.entrySet().stream()
                .filter(entry -> entry.getKey() != SourcePlatform.KICKS_CREW
                        && entry.getKey() != SourcePlatform.POIZON
                        && entry.getKey() != SourcePlatform.TIKTOK_SHOP
                        && entry.getKey() != SourcePlatform.B2B_SHOP)
                .flatMap(entry -> entry.getValue().stream())
                .collect(Collectors.toList());

        batchMissionResult.getUpdatedListings().addAll(otherPlatformListings);
        syncPoizonMissionResult.getUpdatedListings().addAll(poizonListings);
        syncKcMissionResult.getUpdatedListings().addAll(kicksCrewListings);
        syncTtsMissionResult.getUpdatedListings().addAll(ttsListing);
        syncB2bMissionResult.getUpdatedListings().addAll(b2bListing);

        batchMissionResultLists.add(batchMissionResult);
        batchMissionResultLists.add(syncPoizonMissionResult);
        batchMissionResultLists.add(syncKcMissionResult);
        batchMissionResultLists.add(syncTtsMissionResult);
        batchMissionResultLists.add(syncB2bMissionResult);
        return batchMissionResultLists;
    }

    /**
     * @param listings
     * @return
     */
    @Override
    public List<PlatformListing> batchCancelListing(List<PlatformListing> listings) {
        List<PlatformListing> failedCancelListings = new ArrayList<>();

        if (ObjectUtils.isEmpty(listings)) {
            log.info("没有 需要 下架 的 listings");
            return failedCancelListings;
        }

        listings.parallelStream().forEach(listing -> {
            try {
                switch (listing.getPlatform()) {
                    case STOCK_X:
                        stockXListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                    case GOAT_STV:
                    case GOAT_INSTANT_SHIP:
                        goatListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                    case KICKS_CREW:
                        kicksCrewListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                    case EBAY:
                        ebayListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                    case POIZON:
                        poizonListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                    case TIKTOK_SHOP:
                        ttsListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                     case B2B_SHOP:
                        b2bListingUseCase.cancelProduct(listing.getPlatformListingId());
                        break;
                    default:
                        throw new BaseException("unsupported " + listing.getPlatform() + "to cancel listing.");
                }
            } catch (Exception e) {
                log.error("Failed to cancel listing to " + listing.getPlatform()
                        + " error, failed item: " + listing.toString()
                        + ", but other platform mission will continue, reason: " + e
                        + ", stack trace: " + BaseUtils.getCallStack()
                );
                failedCancelListings.add(listing);
            }
        });

        return failedCancelListings;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void batchB2bPriceUpdate(List<B2bUpdatePriceDto> priceEvents) {
        platformListingMapper.batchUpdateB2bPrice(priceEvents);
    }

    /**
     * 待创建列表
     */
    private List<PlatformListing> batchCreateFromKnetListingPool(List<ListingActionItem> pendingCreateResList) {
        List<PlatformListing> createResList = new ArrayList<>();

        Map<SourcePlatform, List<ListingActionItem>> pendingCreateResListGroupByPlatform = pendingCreateResList
                .stream()
                .collect(Collectors.groupingBy(ListingActionItem::getPlatform));

        pendingCreateResListGroupByPlatform.forEach((platform, v) -> {
            try {
                switch (platform) {
                    case STOCK_X:
                        createResList.addAll(stockXListingUseCase.batchCreateListing(v));
                        break;
                    case GOAT_STV:
                    case GOAT_INSTANT_SHIP:
                        createResList.addAll(goatListingUseCase.batchCreateListing(v));
                        break;
                    case KICKS_CREW:
                        createResList.addAll(kicksCrewListingUseCase.batchCreateListing(v));
                        break;
                    case EBAY:
                        createResList.addAll(ebayListingUseCase.batchCreateListing(v));
                        break;
                    case POIZON:
                        createResList.addAll(poizonListingUseCase.batchCreateListing(v));
                        break;
                    case TIKTOK_SHOP:
                        createResList.addAll(ttsListingUseCase.batchCreateListing(v));
                        break;
                    case B2B_SHOP:
                        createResList.addAll(b2bListingUseCase.batchCreateListing(v));
                        break;
                    default:
                        throw new BaseException("unsupported " + platform + "to batch create listing.");
                }
            } catch (Exception e) {
                log.error("Batch create listing to " + platform
                        + " error, failed item: " + v.toString()
                        + ", but other platform mission will continue, reason: " + e
                        + ", stack trace: " + BaseUtils.getCallStack()
                );
            }

        });

        return createResList;
    }

    /**
     * 从寄售商品池拉去的数据更新
     *
     * @param pendingUpdateRes
     * @return
     */
    private List<PlatformListing> batchUpdateFromKnetListingPool(List<ListingActionItem> pendingUpdateRes) {
        List<PlatformListing> updateResult = new ArrayList<>();
        Map<SourcePlatform, List<ListingActionItem>> pendingUpdateResGroupByPlatform = pendingUpdateRes
                .stream()
                .collect(Collectors.groupingBy(ListingActionItem::getPlatform));

        pendingUpdateResGroupByPlatform.forEach((platform, value) -> {
            try {
                switch (platform) {
                    case STOCK_X:
                        updateResult.addAll(stockXListingUseCase.batchUpdateListing(value));
                        break;
                    case GOAT_STV:
                    case GOAT_INSTANT_SHIP:
                        updateResult.addAll(goatListingUseCase.batchUpdateListing(value));
                        break;
                    case KICKS_CREW:
                        updateResult.addAll(kicksCrewListingUseCase.batchUpdateListing(value));
                        break;
                    case EBAY:
                        updateResult.addAll(ebayListingUseCase.batchUpdateListing(value));
                        break;
                    case POIZON:
                        updateResult.addAll(poizonListingUseCase.batchUpdateListing(value));
                        break;
                    case TIKTOK_SHOP:
                        updateResult.addAll(ttsListingUseCase.batchUpdateListing(value));
                        break;
/*                    case B2B_SHOP:
                        updateResult.addAll(b2bListingUseCase.batchUpdateListing(value));
                        break;*/
                    default:
                        throw new BaseException("unsupported " + platform + "to batch update listing.");
                }
            } catch (Exception e) {
                log.error("Batch update listing to " + platform
                        + " error, update detail: " + value.toString()
                        + ", but other platform mission will continue, reason: " + e
                        + ", stack trace: " + BaseUtils.getCallStack()
                );
            }
        });

        return updateResult;
    }

    /**
     * 从Knet 删除调用批量 删除接口
     *
     * @param pendingDeleteRes
     * @return
     */
    private List<PlatformListing> batchDeactivateFromKnetListingPool(List<ListingActionItem> pendingDeleteRes) {
        List<PlatformListing> deleteResult = new ArrayList<>();

        Map<SourcePlatform, List<ListingActionItem>> pendingDeleteResGroupByPlatform = pendingDeleteRes
                .stream()
                .collect(Collectors.groupingBy(ListingActionItem::getPlatform));

        pendingDeleteResGroupByPlatform.forEach((platform, value) -> {
            try {
                switch (platform) {
                    case STOCK_X:
                        deleteResult.addAll(stockXListingUseCase.batchDeleteProduct(value));
                        break;
                    case GOAT_STV:
                    case GOAT_INSTANT_SHIP:
                        deleteResult.addAll(goatListingUseCase.batchDeactivateProduct(value));
                        break;
                    case KICKS_CREW:
                        deleteResult.addAll(kicksCrewListingUseCase.batchDeactivateProduct(value));
                        break;
                    case EBAY:
                        deleteResult.addAll(ebayListingUseCase.batchDeactivateProduct(value));
                        break;
                    case POIZON:
                        deleteResult.addAll(poizonListingUseCase.batchDeactivateProduct(value));
                        break;
                    case TIKTOK_SHOP:
                        deleteResult.addAll(ttsListingUseCase.batchDeactivateProduct(value));
                        break;
                    case B2B_SHOP:
                        deleteResult.addAll(b2bListingUseCase.batchDeactivateProduct(value));
                        break;
                    default:
                        throw new BaseException("unsupported " + platform + "to batch deactivate listing.");
                }
            } catch (Exception e) {
                log.error("Batch deactivate listing to " + platform
                        + " error, deactivate detail: " + value.toString()
                        + ", but other platform mission will continue, reason: " + e
                        + ", stack trace: " + BaseUtils.getCallStack()
                );
            }
        });

        return deleteResult;
    }


}
