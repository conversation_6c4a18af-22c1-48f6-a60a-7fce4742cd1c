package com.hzjm.crosslisting.listing.data.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzjm.crosslisting.listing.entity.UpdateListingItem;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class KnetListingPendingUpdateRes implements Serializable {
    List<UpdateListingItem> stockX = new ArrayList<>();
    List<UpdateListingItem> goat = new ArrayList<>();
    List<UpdateListingItem> goatIs = new ArrayList<>();

    @JSONField(serialize = false)
    public List<UpdateListingItem> allPlatformUpdateItems() {
        List<UpdateListingItem> allUpdateItems = new ArrayList<>();
        allUpdateItems.addAll(stockX);
        allUpdateItems.addAll(goat);
        allUpdateItems.addAll(goatIs);
        return allUpdateItems;
    }
}
