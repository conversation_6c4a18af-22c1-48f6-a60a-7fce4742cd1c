package com.hzjm.crosslisting.listing.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.crosslisting.enums.ListingPricingModifyType;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.listing.data.dao.ListingPricingHistoryMapper;
import com.hzjm.crosslisting.listing.entity.ListingPricingHistory;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.repository.IListingPricingHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class ListingPricingHistoryServiceImpl extends ServiceImpl<ListingPricingHistoryMapper, ListingPricingHistory>  implements IListingPricingHistoryService {
    /**
     * @param listing
     * @return
     */
    @Override
    public boolean saveWithCheck(PlatformListing listing, Boolean modifyByUser) {
        try {
            String listingId = listing.getPlatformListingId();
            List<ListingPricingHistory> latestPricingHistories = this.list(Wrappers.<ListingPricingHistory>lambdaQuery().eq(ListingPricingHistory::getListingId, listingId));

            if (latestPricingHistories.isEmpty()) {
                log.info("无 listing 改价历史 直接 存储 改价数据" + listing.toString());
                ListingPricingHistory history = ListingPricingHistory.createFrom(listing, modifyByUser);
                return baseMapper.insert(history) > 0;
            }

            Optional<ListingPricingHistory> latestPricingHistory = latestPricingHistories
                    .stream()
                    .max(Comparator.comparing(ListingPricingHistory::getGmtCreate));

            // 存在有最新纪录 如果当前的价格与 最新记录相同，就不会存储改价记录，否则新增记录
            ListingPricingHistory latest = latestPricingHistory.get();
            if (Objects.equals(latest.getSalePrice(), listing.getSalePrice())) {
                return false;
            }else{
                ListingPricingHistory history = ListingPricingHistory.createFrom(listing, modifyByUser);
                return baseMapper.insert(history) > 0;
            }
        }catch (Exception e) {
            log.error("记录改价历史失败，listing id: " + listing.getPlatformListingId() + "改价类型: " + modifyByUser);
            return false;
        }
    }

    /**
     * @param listingId
     * @return
     */
    @Override
    public boolean expireListingPricingHistory(String listingId, SourcePlatform platform) {
        if (ObjectUtils.isEmpty(listingId)) {
            log.info("过去 自动跟价历史记录时, 传入的 listingId 为空或者为 null");
            return false;
        }

        List<ListingPricingHistory> allPricingHistories = this.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                .eq(ListingPricingHistory::getListingId, listingId)
                .eq(ListingPricingHistory::getPlatform, platform)
        );

        // 没有 Auto Pricing 的跟价历史
        if (allPricingHistories.isEmpty()) {
            log.info(
                    "过期 所有自动跟价历史时， 未发现 所属 的 listing id: "
                    + listingId
                    + ", 有相应的出价历史"
            );
            return true;
        }

        for (ListingPricingHistory history: allPricingHistories) {
            history.setExpired(true);
        }

        return this.saveOrUpdateBatch(allPricingHistories);
    }
}
