package com.hzjm.crosslisting.listing.repository.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.ListingStatus;
import com.hzjm.crosslisting.listing.data.dao.PlatformListingMapper;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingPageDto;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingQueryDto;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingSearchDto;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingListVo;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingQueryVo;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingVo;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.entity.PlatformListingFlexCount;
import com.hzjm.crosslisting.listing.repository.IKnetProductListingService;
import com.hzjm.crosslisting.listing.repository.IPlatformListingService;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.service.IStockxFlexProdService;
import com.hzjm.service.service.ISysProdSearchService;
import com.hzjm.service.service.ISysProdService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 平台真正的寄售单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-27
 */
@Slf4j
@Service
public class PlatformListingServiceImpl extends ServiceImpl<PlatformListingMapper, PlatformListing> implements IPlatformListingService {

    @Autowired
    IStockxFlexProdService stockxFlexProdService;

    @Resource
    ISysProdSearchService iSysProdSearchService;

    @Resource
    ISysProdService iSysProdService;
    @Resource
    IKnetProductListingService knetProductListingService;

    @Override
    public PlatformListing getByIdWithoutLogic(Integer id) {
        PlatformListing data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该平台真正的寄售单"));
        }

        return data;
    }

    @Override
    public PlatformListingVo getDetail(Integer id) {
        PlatformListing data = getByIdWithoutLogic(id);

        PlatformListingVo vo = new PlatformListingVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean savePlatformListing(PlatformListing dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<PlatformListingListVo> searchList(PlatformListingPageDto dto) {

        LambdaQueryWrapper<PlatformListing> qw = Wrappers.<PlatformListing>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(PlatformListing::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), PlatformListing::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), PlatformListing::getGmtCreate, endTime);

        IPage<PlatformListing> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getPageSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getPageSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<PlatformListingListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                PlatformListingListVo vo = new PlatformListingListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<PlatformListingListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<PlatformListing> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<PlatformListing> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    /**
     * @param platformListings
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public int insertOrUpdateBatch(List<PlatformListing> platformListings) {
        return baseMapper.insertOrUpdateBatch(platformListings);
    }

    /**
     * @param originListingId
     * @param newListingId
     */
    @Override
    public int updatePlatformListingId(String originListingId, String newListingId) {
        LambdaUpdateWrapper<PlatformListing> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PlatformListing::getPlatformListingId, newListingId)
                .eq(PlatformListing::getPlatformListingId, originListingId)
                .eq(PlatformListing::getDelFlag, 0);
        return getBaseMapper().update(null, updateWrapper);
    }


    /**
     * @param dto
     * @return
     */
    @Override
    public IPage<PlatformListingVo> searchList(PlatformListingSearchDto dto) {
        LambdaQueryWrapper<PlatformListing> qw = this.buildQueryWrapper(dto);
        IPage<PlatformListing> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getPageSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getPageSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }
        // 列表为空，直接返回
        if (ObjectUtils.isEmpty(pageResult)
                || ObjectUtils.isEmpty(pageResult.getRecords())
                || pageResult.getRecords().isEmpty()
        ) {
            return new Page<>();
        }
        // 列表存在，获取列表中的 OneId 数据，查询商品对应的规格参数
        List<String> oneIds = pageResult.getRecords()
                .stream()
                .map(PlatformListing::getOneId)
                .filter(oneId -> !ObjectUtils.isEmpty(oneId))
                .collect(Collectors.toList());
        List<SysProd> sysProds = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                .in(!ObjectUtils.isEmpty(oneIds), SysProd::getOneId, oneIds)
                .select(SysProd::getId, SysProd::getOneId
                        , SysProd::getSku
                        , SysProd::getImg
                        , SysProd::getRemarks
                        , SysProd::getSpec
                        , SysProd::getCostPrice
                )
        ).stream().distinct().collect(Collectors.toList());
        List<SysProdSearch> searches = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                .in(!ObjectUtils.isEmpty(oneIds), SysProdSearch::getOneId, oneIds)
                .eq(SysProdSearch::getSearchType, 1)
                .select(SysProdSearch::getOneId
                        , SysProdSearch::getGmtIn
                )
        ).stream().distinct().collect(Collectors.toList());
        Map<String, SysProd> sysProdMap = sysProds.stream().collect(Collectors.toMap(SysProd::getOneId, item -> item
                , (oldValue, newValue) -> newValue));
        Map<String, SysProdSearch> searchMap = searches.stream().collect(Collectors.toMap(SysProdSearch::getOneId, item -> item
                , (oldValue, newValue) -> newValue));
        sysProds.clear();
        searches.clear();
        // 组装返回的VO
        List<PlatformListingVo> platformListingVoArrayList = pageResult.getRecords()
                .parallelStream()
                .map(i -> {
                    PlatformListingVo platformListingVo = new PlatformListingVo();
                    BeanUtils.copyProperties(i, platformListingVo);
                    platformListingVo.setSku(sysProdMap.get(i.getOneId()).getSku());
                    platformListingVo.setProductName(sysProdMap.get(i.getOneId()).getRemarks());
                    platformListingVo.setImg(sysProdMap.get(i.getOneId()).getImg());
                    platformListingVo.setGmtIn(searchMap.get(i.getOneId()).getGmtIn());
                    platformListingVo.setSize(sysProdMap.get(i.getOneId()).getSpec());
                    platformListingVo.setCost(sysProdMap.get(i.getOneId()).getCostPrice());
                    if (ObjectUtils.isNotEmpty(searchMap.get(i.getOneId())) && ObjectUtils.isNotEmpty(searchMap.get(i.getOneId()).getGmtIn())) {
                        platformListingVo.setInWareDays(DateTimeUtils.calculateDaysUntilNow(searchMap.get(i.getOneId()).getGmtIn()));
                    }
                    return platformListingVo;
                }).collect(Collectors.toList());

        IPage<PlatformListingVo> result = new Page<>();
        BeanUtils.copyProperties(pageResult, result);
        result.setRecords(platformListingVoArrayList);
        return result;
    }

    @Override
    public List<PlatformListing> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 统计 flex 的订单数据
     */
    @Override
    public PlatformListingFlexCount selectFlexCount(PlatformListingSearchDto dto) {
        log.info("PlatformListingServiceImpl selectFlexCount start dto ={}", JSON.toJSONString(dto));
        LambdaQueryWrapper<PlatformListing> qw = this.buildQueryWrapper(dto);
        qw.select(PlatformListing::getId, PlatformListing::getSaleStatus);
        List<PlatformListing> list = list(qw);
        if (ObjectUtils.isEmpty(list)) {
            return new PlatformListingFlexCount();
        }
        PlatformListingFlexCount flexCount = new PlatformListingFlexCount()
                .setTotalInventory(list.size())
                .setActiveListings(list.stream().filter(i -> !ObjectUtils.isEmpty(i.getSaleStatus()) && i.getSaleStatus() == ListingStatus.ACTIVE).count())
                .setInActiveListings(list.stream().filter(i -> !ObjectUtils.isEmpty(i.getSaleStatus()) && i.getSaleStatus() == ListingStatus.INACTIVE).count());
        log.info("PlatformListingServiceImpl selectFlexCount end");
        return flexCount;
    }

    /**
     * 查询所有 KnetProductListing 已经设置为下架 但是 三方listing 没有及时下架的数据
     *
     * @return 符合条件的 PlatformListing 列表
     */
    @Override
    public List<PlatformListing> searchNotDeactivatePlatformListings() {
        return getBaseMapper().selectNotDeactivatePlatformListings();
    }

    /**
     * @return
     */
    @Override
    public List<String> getPendingManageListingTemplateIds() {
        return getBaseMapper().getPendingManageListingTemplateIds();
    }

    /**
     * 只更新 lisitng 的 销售价格 以及 销售状态，且 MATCHED 不会被更新
     *
     * @param platformListings
     */
    @Override
    public void updateListingPriceAndStatus(List<PlatformListing> platformListings) {
        getBaseMapper().updateListingPriceAndStatus(platformListings);
    }

    /**
     * @return
     */
    @Override
    public List<PlatformListing> selectNeedManageActivateListings() {
        return getBaseMapper().selectNeedManageActivateListings();
    }


    private static final List<ListingStatus> AVAILABLE_DISPLAY_STATUS = Arrays.asList(
            ListingStatus.ACTIVE,
            ListingStatus.INACTIVE
    );

    /**
     * 构建查询条件
     *
     * @param dto req
     * @return 查询条件
     */
    private LambdaQueryWrapper<PlatformListing> buildQueryWrapper(PlatformListingSearchDto dto) {
        // sku查询
        List<String> oneIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(dto.getSku())) {
            List<SysProd> sysProds = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().select(SysProd::getOneId).like(SysProd::getSku, dto.getSku()));
            oneIds = sysProds.stream().distinct().map(SysProd::getOneId).collect(Collectors.toList());
        }
        //筛选 非最低价产品
        List<Integer> knetProductIdList = knetProductListingService.getKnetProductIdList(dto, dto.getUserId());
        return Wrappers.<PlatformListing>lambdaQuery()
                .orderByDesc(PlatformListing::getGmtModify)
                .orderByDesc(PlatformListing::getId)
                .in(PlatformListing::getSaleStatus, AVAILABLE_DISPLAY_STATUS) // 只显示 可显示的数据
                .eq(PlatformListing::getAccount, ListingAccount.STOCKX_FLEX) // 只能搜索 stockX Flex 的数据
                .in(!ObjectUtils.isEmpty(dto.getListingIds()), PlatformListing::getPlatformListingId, dto.getListingIds())
                .eq(!ObjectUtils.isEmpty(dto.getUserId()), PlatformListing::getShopUserId, dto.getUserId())
                .eq(!ObjectUtils.isEmpty(dto.getListingStatus()), PlatformListing::getSaleStatus, dto.getListingStatus())
                .eq(!ObjectUtils.isEmpty(dto.getBatchId()), PlatformListing::getBatchId, dto.getBatchId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), PlatformListing::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(dto.getEndTime()), PlatformListing::getGmtCreate, dto.dealEndTime())
                .in(!ObjectUtils.isEmpty(oneIds), PlatformListing::getOneId, oneIds)
                .in(ObjectUtils.isNotEmpty(knetProductIdList), PlatformListing::getId, knetProductIdList);
    }


    @Override
    public List<PlatformListing> queryMarketDataByKnetListings(List<String> knetListingIds) {
        //1.获取当前商品的市场数据
        LambdaQueryWrapper<PlatformListing> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .in(PlatformListing::getKnetListingId, knetListingIds)
                .in(PlatformListing::getSaleStatus,
                        ListingStatus.ACTIVE,
                        ListingStatus.PENDING);
        return this.list(queryWrapper);
    }

    @Override
    public IPage<PlatformListingQueryVo> queryPlatformListings(PlatformListingQueryDto dto) {
        // 构建查询条件
        LambdaQueryWrapper<PlatformListing> qw = Wrappers.<PlatformListing>lambdaQuery();

        // 添加查询条件
        qw.eq(!ObjectUtils.isEmpty(dto.getSaleStatus()), PlatformListing::getSaleStatus, dto.getSaleStatus())
          .eq(!ObjectUtils.isEmpty(dto.getPlatform()), PlatformListing::getPlatform, dto.getPlatform())
          .eq(!ObjectUtils.isEmpty(dto.getAccount()), PlatformListing::getAccount, dto.getAccount())
          .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), PlatformListing::getGmtCreate, dto.getBeginTime())
          .lt(!ObjectUtils.isEmpty(dto.getEndTime()), PlatformListing::getGmtCreate, dto.dealEndTime())
          .orderByDesc(PlatformListing::getId); // 按ID降序排列

        // 分页查询
        IPage<PlatformListing> pageResult = new Page<>();
        if (!ObjectUtils.isEmpty(dto.getPageSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getPageSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        // 转换为VO
        List<PlatformListingQueryVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                PlatformListingQueryVo vo = new PlatformListingQueryVo();
                BeanUtils.copyProperties(data, vo);

                // 转换枚举为字符串
                if (data.getSaleStatus() != null) {
                    vo.setSaleStatus(data.getSaleStatus().toString());
                }
                if (data.getPlatform() != null) {
                    vo.setPlatform(data.getPlatform().toString());
                }
                if (data.getAccount() != null) {
                    vo.setAccount(data.getAccount().toString());
                }

                voList.add(vo);
            });
        }

        // 构建返回结果
        IPage<PlatformListingQueryVo> voResult = new Page<>();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }
}
