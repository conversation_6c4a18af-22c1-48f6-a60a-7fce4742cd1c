package com.hzjm.crosslisting.listing.usecase.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.crosslisting.enums.KnetProductListingStatus;
import com.hzjm.crosslisting.listing.data.dto.B2bUpdatePriceDto;
import com.hzjm.crosslisting.listing.entity.KnetProductListing;
import com.hzjm.crosslisting.listing.repository.IKnetProductListingSaveService;
import com.hzjm.crosslisting.listing.usecase.IPlatformListingBatchService;
import com.hzjm.crosslisting.listing.usecase.ITaskB2bListingService;
import com.hzjm.crosslisting.pricing.usecase.revenue.impl.B2bPriceHandler;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import com.hzjm.service.entity.SysUpdatePriceEvents;
import com.hzjm.service.service.ISysUpdatePriceEventsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/14 15:04
 * @description:
 */
@Slf4j
@Service
public class TaskB2bListingServiceImpl implements ITaskB2bListingService {
    @Resource
    private B2bPriceHandler b2bPriceHandler;
    @Resource
    private IKnetProductListingSaveService knetProductListingSaveService;
    @Resource
    private ISysUpdatePriceEventsService iSysUpdatePriceEventsService;
    @Resource
    private IPlatformListingBatchService iPlatformListingBatchService;

    @Override
    public void batchCreateB2bListing(int batchSize) {
        if (batchSize <= 0) {
            batchSize = 50;
        }
        log.info("开始批量上架b2b平台商品，批量大小: {}", batchSize);
        // 1. 从knetProductListing表中查询出需要上架的商品
        LambdaQueryWrapper<KnetProductListing> knetQuery = Wrappers.lambdaQuery();
        knetQuery
                .select(KnetProductListing::getB2bProductTemplateId
                        , KnetProductListing::getB2bVariantId
                        , KnetProductListing::getB2bSalePrice
                        , KnetProductListing::getB2bPendingAction
                        , KnetProductListing::getB2bStatus
                        , KnetProductListing::getId
                        , KnetProductListing::getSku
                        , KnetProductListing::getSize
                )
                .in(KnetProductListing::getKnetListingStatus, KnetProductListingStatus.CREATED, KnetProductListingStatus.UPDATED)
                .isNull(KnetProductListing::getB2bProductTemplateId)
                .isNull(KnetProductListing::getB2bVariantId)
                .isNull(KnetProductListing::getB2bSalePrice)
                .orderByAsc(KnetProductListing::getId)
                .last("limit " + batchSize);
        List<KnetProductListing> productListings = knetProductListingSaveService.list(knetQuery);
        if (productListings.isEmpty()) {
            log.info("没有需要上架的商品");
            return;
        }
        // 2.组装数据，将数据补充完整，可以让batchCreateNewListing 定时任务执行
        productListings.forEach(KnetProductListing::createB2bListing);
        // 3. 批量更新数据
        try {
            if (CollUtil.isNotEmpty(productListings)) {
                int updateCount = knetProductListingSaveService.updateBuy2BuyListings(productListings);
                log.info("批量更新商品B2B信息成功，共更新{}条数据", updateCount);
            }
        } catch (Exception e) {
            log.error("批量更新商品B2B信息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void updateListingPrice(List<SysUpdatePriceEvents> priceEvents) {
        List<String> knetListings = priceEvents.stream().map(SysUpdatePriceEvents::getKnetListingId).collect(Collectors.toList());
        if (CollUtil.isEmpty(knetListings)) {
            return;
        }
        Map<String, KnetProductPrice> knetProductPriceMap = b2bPriceHandler.calculateSalePrice(knetListings);
        if (CollUtil.isEmpty(knetProductPriceMap)) {
            log.error("没有找到当前商品的市场数据，商品ids: {}", String.join(",", knetListings));
            return;
        }
        List<B2bUpdatePriceDto> b2bUpdatePrices = knetProductPriceMap
                .entrySet()
                .stream()
                .map(entry -> new B2bUpdatePriceDto(entry.getKey(), entry.getValue().getAmountUsdCents()))
                .collect(Collectors.toList());
        // 4. 批量更新数据
        try {
            iPlatformListingBatchService.batchB2bPriceUpdate(b2bUpdatePrices);
            iSysUpdatePriceEventsService.successUpdatePriceTasks(priceEvents);
            log.info("批量更新B2B价格成功，共更新{}条数据", priceEvents.size());
        } catch (Exception e) {
            iSysUpdatePriceEventsService.failUpdatePriceTasks(priceEvents);
            log.error("批量更新B2B价格失败: {}", e.getMessage(), e);
        }
    }
}
