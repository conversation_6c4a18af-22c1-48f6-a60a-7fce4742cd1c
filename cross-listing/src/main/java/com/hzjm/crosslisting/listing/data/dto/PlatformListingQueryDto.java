package com.hzjm.crosslisting.listing.data.dto;

import com.hzjm.crosslisting.common.PageBaseSearchDto;
import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.ListingStatus;
import com.hzjm.crosslisting.enums.SourcePlatform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台寄售单查询请求DTO
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PlatformListingQueryDto", description = "平台寄售单查询请求")
public class PlatformListingQueryDto extends PageBaseSearchDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售状态")
    private ListingStatus saleStatus;

    @ApiModelProperty(value = "平台")
    private SourcePlatform platform;

    @ApiModelProperty(value = "账号")
    private ListingAccount account;
}
