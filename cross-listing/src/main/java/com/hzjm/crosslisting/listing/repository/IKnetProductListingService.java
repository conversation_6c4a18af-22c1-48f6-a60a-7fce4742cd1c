package com.hzjm.crosslisting.listing.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingSearchDto;
import com.hzjm.crosslisting.listing.entity.KnetProductListing;
import com.hzjm.crosslisting.product.data.vo.B2bKnetInventoryDataVo;

import java.util.List;

public interface IKnetProductListingService extends IService<KnetProductListing> {

    void deactivateAndReleaseSysProd(List<KnetProductListing> knetProductListings);

    /**
     * 筛选 非最低价产品
     *
     * @param request rq
     * @param userId  userId
     * @return list
     */
    List<Integer> getKnetProductIdList(PlatformListingSearchDto request, Integer userId);

    /**
     * 获取 sku 下对应的oneId
     *
     * @param skus sku
     * @return B2bKnetInventoryDataVos
     */
    List<B2bKnetInventoryDataVo> getInventoryDataBySku(List<String> skus);
}
