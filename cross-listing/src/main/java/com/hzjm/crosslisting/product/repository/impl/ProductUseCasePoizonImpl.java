package com.hzjm.crosslisting.product.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.crosslisting.enums.*;
import com.hzjm.crosslisting.product.entity.KnetProduct;
import com.hzjm.crosslisting.product.entity.KnetProductMarketData;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import com.hzjm.crosslisting.product.entity.SizeOption;
import com.hzjm.crosslisting.product.repository.IKnetProductService;
import com.hzjm.crosslisting.product.repository.IProductUseCase;
import com.hzjm.crosslisting.utils.SizeConvertor;
import com.hzjm.crosslisting.utils.StringProcessedUtils;
import com.hzjm.poizon.listing.dto.BiddingType;
import com.hzjm.poizon.product.dto.resposne.BatchBidReference;
import com.hzjm.poizon.product.dto.resposne.BidReference;
import com.hzjm.poizon.product.dto.resposne.PoizonProduct;
import com.hzjm.poizon.product.dto.resposne.SkuInfo;
import com.hzjm.poizon.product.usecase.IPoizonProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductUseCasePoizonImpl implements IProductUseCase {

    @Autowired
    IPoizonProductService productService;

    @Autowired
    IKnetProductService knetProductService;

    @Autowired
    SizeConvertor sizeConvertor;

    @Override
    public List<KnetProduct> searchProduct(String sku) {
        String processed = StringProcessedUtils.processSkuForKicksCrew(sku);

        PoizonProduct poizonProduct = productService.getPoizonProduct(processed).stream().findFirst().orElse(null);

        // 没有 匹配的数据 或 没有匹配的 variant 数据
        if (ObjectUtils.isEmpty(poizonProduct) || ObjectUtils.isEmpty(poizonProduct.getSkuInfoList())) {
            log.info("No available poizon product match sku: " + sku);
            return new ArrayList<>();
        }

        log.info("poizon product: " + poizonProduct);

        List<KnetProduct> createdKnetProduct = new ArrayList<>();

        poizonProduct.getSkuInfoList().forEach(variant -> {
            // 获取 Poizon size 的 数据
            SkuInfo.RegionSalePvInfo poizonSize = variant.getRegionSalePvInfoList()
                    .stream()
                    .filter(a -> Objects.equals(a.getSort(), variant.getSort()))
                    .findFirst()
                    .orElse(null);

            // 没有对应的 size 数据跳过此次循环
            if (ObjectUtils.isEmpty(poizonSize) || ObjectUtils.isEmpty(poizonSize.getSizeInfos())) {
                log.error("Poizon have not a valid size infos: " + variant);
                return;
            }

            SkuInfo.RegionSalePvInfo.SizeInfo size = poizonSize
                    .getSizeInfos()
                    .stream()
                    .filter(sizeInfo -> {
                        // 无法判断类别的直接 return
                        if (ObjectUtils.isEmpty(poizonProduct.getSpuInfo().getFit())) {
                            return false;
                        }

                        // Birken Stock 的鞋子以 欧码进行映射
                        if (ObjectUtils.isNotEmpty(poizonProduct.getSpuInfo().getBrandName())
                                && Objects.equals("Birkenstock", poizonProduct.getSpuInfo().getBrandName())) {
                            return sizeInfo.getSizeKey().contains("EU");
                        }

                        // 此类型的 size 直接 返回 true
                        if (sizeInfo.getSizeKey().contains("SIZE")) {
                            return true;
                        }

                        String fit = poizonProduct.getSpuInfo().getFit();
                        switch (fit) {
                            case "Unisex":
                            case "Women":
                                return sizeInfo.getSizeKey().contains("US Women");
                            case "Men":
                                return sizeInfo.getSizeKey().contains("US Men");
                            case "Grade School":
                            case "Pre-school":
                                return sizeInfo.getSizeKey().contains("US Kids");
                            case "Baby":
                                return sizeInfo.getSizeKey().contains("US") || sizeInfo.getSizeKey().contains("US Kids");
                        }

                        return false;
                    })
                    .filter(sizeInfo -> {
                        // 无法判断类别的直接 return
                        if (ObjectUtils.isEmpty(poizonProduct.getSpuInfo().getFit())) {
                            return false;
                        }

                        // Birken Stock 的鞋子以 欧码进行映射
                        if (ObjectUtils.isNotEmpty(poizonProduct.getSpuInfo().getBrandName())
                                && Objects.equals("Birkenstock", poizonProduct.getSpuInfo().getBrandName())) {
                            return sizeInfo.getSizeKey().contains("EU");
                        }

                        // 此类型的 size 直接 返回 true
                        if (sizeInfo.getSizeKey().contains("SIZE")) {
                            return true;
                        }

                        String fit = poizonProduct.getSpuInfo().getFit();
                        switch (fit) {
                            case "Unisex":
                                // 去查看 该 sku 在goat 是属于 男码还是女码
                                boolean isWomen = true; // 默认印射女码
                                KnetProduct selectedProduct = knetProductService.getOne(Wrappers.<KnetProduct>lambdaQuery()
                                        .eq(KnetProduct::getSku, processed)
                                        .eq(KnetProduct::getPlatform, SourcePlatform.GOAT_INSTANT_SHIP)
                                        .last("LIMIT 1")
                                );
                                // 如果 goat 存储的 是 男码 则进行标记
                                if (ObjectUtils.isNotEmpty(selectedProduct)) {
                                    if (selectedProduct.getGender() == SizeGender.MEN) {
                                        isWomen = false;
                                    }
                                }

                                return isWomen ? sizeInfo.getSizeKey().contains("US Women") : sizeInfo.getSizeKey().contains("US Men");
                            case "Women":
                                return sizeInfo.getSizeKey().contains("US Women");
                            case "Men":
                                return sizeInfo.getSizeKey().contains("US Men");
                            case "Grade School":
                            case "Pre-school":
                                return sizeInfo.getSizeKey().contains("US Kids");
                            case "Baby":
                                return sizeInfo.getSizeKey().contains("US") || sizeInfo.getSizeKey().contains("US Kids");
                        }

                        return false;
                    })
                    .findFirst()
                    .orElse(null);

            log.info("size info: " + size);

           if (ObjectUtils.isEmpty(size)) {
               log.error("No US size info, " + variant);
               return;
           }

           // POIZON Gender
           SizeGender gender = SizeGender.parse(poizonProduct.getSpuInfo().getFit());

           createdKnetProduct.add(KnetProduct.createFrom(variant, poizonProduct.getSpuInfo(), size.getValue(), gender));
        });

        return createdKnetProduct;
    }

    /**
     * 在平台上搜索 Product
     *
     * @param sku sku
     * @return
     */
    public List<KnetProduct> searchProductMark1(String sku) {
        String processed = StringProcessedUtils.processSkuForKicksCrew(sku);

        // 因为 POIZON 只支持 欧码 显示所以我们需要 按照我们自己 数据库的数据进行对照印射，我们数据库中没有的数据不能进行转换
        List<KnetProduct> knetProducts = getExistedKnetProducts(processed);

        if (ObjectUtils.isEmpty(knetProducts)) {
            log.info("create poizon knet product must exist other platform product in database: " + sku);
            return new ArrayList<>();
        }

        PoizonProduct poizonProduct = productService.getPoizonProduct(processed).stream().findFirst().orElse(null);

        // 没有 匹配的数据 或 没有匹配的 variant 数据
        if (ObjectUtils.isEmpty(poizonProduct) || ObjectUtils.isEmpty(poizonProduct.getSkuInfoList())) {
            log.info("No available poizon product match sku: " + sku);
            return new ArrayList<>();
        }

        // 根据是否包含特殊字符来判断 是否是 adidas original
        boolean isAdidasOriginal = containsSpecialCharacters(poizonProduct.getSkuInfoList());

        // 将 存在 knet product an 欧码 size 进行 Map
        Map<String, KnetProduct> knetProductMapBySize = knetProducts.stream()
                .map(a -> {
                    try {
                        String brand = isAdidasOriginal ? "adidas original" : a.getBrand();
                        SizeOption sizeOption = new SizeOption(a.getSize(), a.getGender(), SizeUnit.US);
                        String convertedSize = sizeConvertor.convertSize(sizeOption, brand, SizeUnit.EU);
                        return new AbstractMap.SimpleEntry<>(convertedSize, a);
                    } catch (Exception e) {
                        // 记录异常或进行其他处理
                        System.err.println("Size conversion failed for product: " + a + ", error: " + e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull) // 过滤掉转换失败的条目
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e, r) -> r));

        List<KnetProduct> createdKnetProduct = new ArrayList<>();
        poizonProduct.getSkuInfoList().forEach(variant -> {
            // 获取 Poizon size 的 数据
            SkuInfo.RegionSalePvInfo poizonSize = variant.getRegionSalePvInfoList()
                    .stream()
                    .filter(a -> Objects.equals(a.getSort(), variant.getSort()))
                    .findFirst()
                    .orElse(null);

            // 没有对应的 size 数据跳过此次循环
            if (ObjectUtils.isEmpty(poizonSize)) {
                log.error("Poizon have not a valid size info: " + variant);
                return;
            }

            KnetProduct matchedKnetProduct = knetProductMapBySize.get(poizonSize.getValue());

            // Poizon 有数据，但数据库没有对应的数据
            if (ObjectUtils.isEmpty(matchedKnetProduct)) {
                log.error("Poizon have size info, but there's no matched knet product with size : " + variant);
                return;
            }

            createdKnetProduct.add(KnetProduct.createFrom(variant, poizonProduct.getSpuInfo(), matchedKnetProduct.getSize(), matchedKnetProduct.getGender()));
        });

        return createdKnetProduct;
    }

    private List<KnetProduct> getExistedKnetProducts(String sku) {
        List<SourcePlatform> platforms = Arrays.asList(
                SourcePlatform.GOAT_INSTANT_SHIP,
                SourcePlatform.STOCK_X,
                SourcePlatform.KICKS_CREW
        );

        List<KnetProduct> knetProducts = new ArrayList<>();

        for (SourcePlatform platform : platforms) {
            knetProducts = knetProductService.list(Wrappers.<KnetProduct>lambdaQuery()
                    .eq(KnetProduct::getSku, StringProcessedUtils.processSkuFormat(sku))
                    .eq(KnetProduct::getPlatform, platform)
            );

            if (!ObjectUtils.isEmpty(knetProducts)) {
                break;
            }
        }

        return knetProducts;
    }

    /**
     *  是否包含 adidas 的半码特殊字符
     * @param skuInfoList
     * @return
     */
    private boolean containsSpecialCharacters(List<SkuInfo> skuInfoList) {
        return skuInfoList.stream()
                .map(skuInfo -> skuInfo.getRegionSalePvInfoList()
                        .stream()
                        .filter(a -> Objects.equals(a.getSort(), skuInfo.getSort()))
                        .findFirst()
                        .orElse(null)
                )
                .filter(Objects::nonNull)
                .anyMatch(regionSalePvInfo -> {
                    String value = regionSalePvInfo.getValue();
                    return value != null && (value.contains("⅓") || value.contains("⅔"));
                });
    }

    /**
     * 获取指定鞋款（包含码数）的 市场数据，比如 最高求购价， 最低售价 等
     *
     * @param knetProduct productId, StockX 上的 VariantId， Goat上的 ProductTemplateId.
     * @param checkResult
     * @return
     */
    @Cacheable(
            value = "ProductMarketData",
            key = "'poizon:' + #knetProduct.sku + ':' + #knetProduct.size",
            unless = "#result == null")
    @Override
    public KnetProductMarketData getProductMarketData(KnetProduct knetProduct, Integer checkResult) {

        Long globalSkuId = Long.valueOf(knetProduct.getVariantId());
        if (ObjectUtils.isEmpty(globalSkuId)) {
            log.error("获取 POIZON 最新市场数据失败, sku: " + knetProduct.getSku()
                    + ", size: " + knetProduct.getSize()
                    + ", reason: 转换 globalSkuId 失败"
            );
            return null;
        }

        try {
            BidReference bidReference = productService.getBidReference(globalSkuId, BiddingType.SHIP_TO_VERIFY);

            return new KnetProductMarketData(
                    knetProduct.getSku(),
                    knetProduct.getSize(),
                    new KnetProductPrice(bidReference.getLocalMinPrice()), // 存放 local min price
                    new KnetProductPrice(bidReference.getGlobalMinPrice()),
                    new KnetProductPrice(0),
                    new KnetProductPrice(bidReference.getHighDemandPrice()),
                    new KnetProductPrice(0),
                    new KnetProductPrice(0),
                    KnetBoxCondition.GOOD_CONDITION,
                    KnetShoeCondition.NEW_NO_DEFECTS,
                    SourcePlatform.POIZON);
        }catch (Exception e) {
            log.error("获取 POIZON 最新市场数据失败, sku: " + knetProduct.getSku()
                    + ", size: " + knetProduct.getSize()
                    + ", reason: " + e + ", stack trace: " + BaseUtils.getCallStack()
            );
        }
        return null;
    }

    /**
     * 获取所有 sku 下 variants 的 市场数据
     *
     * @param knetProduct 对应平台尺码的数据
     * @return
     */
    @Override
    public List<KnetProductMarketData> getAllVariantsProductMarketData(KnetProduct knetProduct) {

        // 获取已有的产品列表
        List<KnetProduct> existedKnetProducts = knetProductService.list(Wrappers.<KnetProduct>lambdaQuery()
                .eq(KnetProduct::getSku, knetProduct.getSku())
                .eq(KnetProduct::getPlatform, SourcePlatform.POIZON)
        );

        // 如果没有找到产品，返回空列表
        if (existedKnetProducts == null || existedKnetProducts.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用线程安全的集合来存储市场数据
        List<KnetProductMarketData> marketDataList = new ArrayList<>(existedKnetProducts.size());

        // 将产品列表按每组20个切分
        List<List<KnetProduct>> knetProductMapChunks = BaseUtils.sliceListIntoChunks(existedKnetProducts, 20);

        // 遍历每个切片
        for (List<KnetProduct> chunk : knetProductMapChunks) {
            // 转换成以variant id为key的map
            Map<Long, KnetProduct> chunkMap = chunk.stream()
                    .collect(Collectors.toMap(a -> Long.valueOf(a.getVariantId()), a -> a, (e, r) -> r));

            // 获取远程市场价数据
            List<BatchBidReference> references;
            try {
                references = productService.getBidReferenceBatch(new ArrayList<>(chunkMap.keySet()), BiddingType.SHIP_TO_VERIFY);
            } catch (Exception e) {
                log.error("尝试为SKU: {} 批量更新POIZON价格失败，原因: {}", knetProduct.getSku(), e.getMessage(), e);
                continue;
            }

            // 如果没有获取到参考数据，继续下一次循环
            if (references == null || references.isEmpty()) {
                continue;
            }

            // 并行处理参考数据
            references.parallelStream().forEach(reference -> {
                try {
                    int globalMinPrice = reference.getGlobalMinPrice() != null ? reference.getGlobalMinPrice() : 0;
                    int localMinPrice = reference.getLocalMinPrice() != null ? reference.getLocalMinPrice() : 0;

                    KnetProduct knetProductFromMap = chunkMap.get(reference.getGlobalSkuId());
                    if (knetProductFromMap != null) {
                        synchronized (marketDataList) {
                            marketDataList.add(new KnetProductMarketData(
                                    knetProduct.getSku(),
                                    knetProductFromMap.getVariantId(),
                                    new KnetProductPrice(localMinPrice), // 存放local min price
                                    new KnetProductPrice(globalMinPrice),
                                    new KnetProductPrice(0),
                                    new KnetProductPrice(0),
                                    new KnetProductPrice(0),
                                    new KnetProductPrice(0),
                                    KnetBoxCondition.GOOD_CONDITION,
                                    KnetShoeCondition.NEW_NO_DEFECTS,
                                    SourcePlatform.POIZON
                            ));
                        }
                    }
                } catch (Exception e) {
                    log.error("尝试为SKU: {} 批量更新POIZON价格失败，原因: {}", knetProduct.getSku(), e.getMessage(), e);
                }
            });
        }

        return marketDataList;
    }



}
