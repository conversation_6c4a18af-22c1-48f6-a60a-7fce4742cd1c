package com.hzjm.crosslisting.product.entity;

import cn.hutool.core.util.NumberUtil;
import com.hzjm.crosslisting.enums.KnetCurrencyCode;
import com.hzjm.crosslisting.pricing.entity.RevenueDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Comparator;
import java.util.List;

import static com.hzjm.crosslisting.pricing.common.B2bPlatformFee.B2B_PLATFORM_FEE;

@Slf4j
@Data
@AllArgsConstructor
public class KnetProductPrice implements Serializable {

    @ApiModelProperty("货币单位价格")
    KnetCurrencyCode currencyCode;

    @ApiModelProperty("价格")
    String amount;

    @ApiModelProperty("美分价格，例如一双鞋售卖 214.50 美刀， 那么它的值应该为 21450。")
    int amountUsdCents;

    public KnetProductPrice() {

    }

    /**
     * 只输入 amount 则默认为美元
     *
     * @param amount 价格
     */
    public KnetProductPrice(String amount) {
        this.amount = amount;
        this.currencyCode = KnetCurrencyCode.USD;
        // 从字符串转 double 在向上取整
        double value = Double.parseDouble(amount) * 100;
        this.amountUsdCents = (int) Math.floor(value);
    }

    /**
     * 值输入美分价格将默认为美元
     *
     * @param amountUsdCents 美分单位
     */
    public KnetProductPrice(int amountUsdCents) {
        this.amountUsdCents = amountUsdCents;
        this.currencyCode = KnetCurrencyCode.USD;
        this.amount = String.format("%.2f", (double) amountUsdCents / 100);
    }

    // TODO: 这里可能需要实现 货币单位 和美分之间 转换的逻辑
    public KnetProductPrice(String amount, KnetCurrencyCode currencyCode) {
        this.amount = amount;
        this.currencyCode = currencyCode;
    }

    public int amountUSD() {
        return this.amountUsdCents / 100;
    }

    /**
     * 获取 B2B 平台的到手价
     * 每个平台到手价最低*b2b费率
     *
     * @param revenueDetailList revenueDetailList
     * @return KnetProductPrice
     */
    public static KnetProductPrice calculateMinimumB2bPrice(List<RevenueDetail> revenueDetailList) {
        revenueDetailList.forEach(detail -> {
            log.info("平台[{}]到手价: {}", detail.getPlatform(), detail.getOwning());
        });
        //3.计算价格 取平台中最低的价格，* 10%
        KnetProductPrice lowPrice = revenueDetailList
                .stream()
                .map(RevenueDetail::getOwning)
                .min(Comparator.comparingInt(KnetProductPrice::getAmountUsdCents))
                .orElse(new KnetProductPrice(0));
        log.info("平台 lowPrice: {}", lowPrice);
        int b2bSalePrice = (int) NumberUtil.mul(Double.valueOf(lowPrice.getAmountUsdCents()), B2B_PLATFORM_FEE);
        int b2bSalePriceFinal = (int) Math.ceil(b2bSalePrice / 100.0) * 100;
        log.info(" 计算后的b2bSalePrice: {}，最终小数点向上取整 mulled：[{}]", b2bSalePrice, b2bSalePriceFinal);
        return new KnetProductPrice(b2bSalePriceFinal);
    }
}
