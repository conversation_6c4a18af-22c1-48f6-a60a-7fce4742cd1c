package com.hzjm.crosslisting.pricing.data.dto;

import com.hzjm.crosslisting.enums.BulkPricingModifyMode;
import com.hzjm.crosslisting.enums.FollowPriceMode;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BulkModifyPricingInfo {

    BulkPricingModifyMode pricingModifyMode;

    FollowPriceMode followPriceMode;

    List<SourcePlatform> salePlatforms;

    @ApiModelProperty("成本")
    KnetProductPrice cost;

    @ApiModelProperty("利润率")
    String profitRate;

    @ApiModelProperty("可以通过设置差价对最高到手价以外的平台进行改价")
    KnetProductPrice priceDifference;

}
