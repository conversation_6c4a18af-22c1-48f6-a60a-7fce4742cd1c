package com.hzjm.crosslisting.pricing.usecase.bulkmodify.impl;

import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.pricing.data.dto.BulkModifyPricingDto;
import com.hzjm.crosslisting.pricing.data.dto.BulkModifyPricingInfo;
import com.hzjm.crosslisting.pricing.data.vo.BulkModifyPricingVo;
import com.hzjm.crosslisting.pricing.usecase.bulkmodify.IBulkPricingModifyStrategy;
import com.hzjm.crosslisting.pricing.usecase.revenue.PricingDetailStrategyContext;
import com.hzjm.crosslisting.product.entity.KnetProductMarketDataTotal;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.analysis.function.Max;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class ProfitControlStrategy implements IBulkPricingModifyStrategy {

    @Autowired
    PricingDetailStrategyContext pricingDetailStrategyContext;

    /**
     *  成本控制，根据用户输入的成本，利润率， 以及市场数据里 最低售价，最高求购价
     *  返回一个应该修改的价格
     * 根据 市场数据 和 用户 改价策略计算用户的出售价
     * @param modifyPricingItem
     * @return
     */
    @Override
    public BulkModifyPricingVo.BulkModifyPricingItem calculateSalePrice(BulkModifyPricingDto.BulkModifyPricingItem modifyPricingItem, Integer shopId) {
        BulkModifyPricingVo.BulkModifyPricingItem bulkModifyPricingResItem = new BulkModifyPricingVo.BulkModifyPricingItem();
        KnetProductMarketDataTotal marketData = modifyPricingItem.getMarketDataTotal(); // 市场售价数据
        BulkModifyPricingInfo pricingInfo = modifyPricingItem.getPricingInfo(); //用户选择的计价模式和信息

        List<SourcePlatform> salePlatforms = modifyPricingItem.getPricingInfo().getSalePlatforms();
        if (marketData.getGoatMarketData() != null && salePlatforms.contains(SourcePlatform.GOAT_STV)) {
            // 如果有 goat STV 的数据
            if (marketData.getGoatMarketData().getLowestAskPrice() != null) {
                KnetProductPrice salePrice = judgeLowestAskPrice(
                        marketData.getGoatMarketData().getLowestAskPrice(),
                        pricingInfo.getCost(),
                        pricingInfo.getProfitRate(),
                        ListingAccount.GOAT_STV_CONSIGNMENT,
                        shopId
                );
                bulkModifyPricingResItem.setGoat(salePrice);
            }

            // 如果有 goat instant ship 的数据
            if (marketData.getGoatMarketData().getInstantShipLowestAskPrice() != null && salePlatforms.contains(SourcePlatform.GOAT_INSTANT_SHIP)) {
                KnetProductPrice salePriceInstant = judgeLowestAskPrice(
                        marketData.getGoatMarketData().getInstantShipLowestAskPrice(),
                        pricingInfo.getCost(),
                        pricingInfo.getProfitRate(),
                        ListingAccount.GOAT_INSTANT_SHIP_CONSIGNMENT,
                        shopId
                );
                bulkModifyPricingResItem.setGoatIs(salePriceInstant);
            }
        }

        // 如果有 stockX 的数据
        if (marketData.getStockXMarketData() != null && salePlatforms.contains(SourcePlatform.STOCK_X)) {
            KnetProductPrice salePrice = judgeLowestAskPrice(
                    marketData.getStockXMarketData().getLowestAskPrice(),
                    pricingInfo.getCost(),
                    pricingInfo.getProfitRate(),
                    ListingAccount.STOCK_X_CONSIGNMENT,
                    shopId
            );
            bulkModifyPricingResItem.setStockX(salePrice);
        }

        // 如果有 Kicks Crew 的数据
        if (marketData.getKcMarketData() != null && salePlatforms.contains(SourcePlatform.KICKS_CREW)) {
            KnetProductPrice salePrice = judgeLowestAskPrice(
                    marketData.getKcMarketData().getLowestAskPrice(),
                    pricingInfo.getCost(),
                    pricingInfo.getProfitRate(),
                    ListingAccount.KICKS_CREW_CONSIGNMENT,
                    shopId
            );
            bulkModifyPricingResItem.setKc(salePrice);
        }

        // 如果有 Ebay 的数据
        if (marketData.getEbayMarketData() != null && salePlatforms.contains(SourcePlatform.EBAY)) {
            KnetProductPrice salePrice = judgeLowestAskPrice(
                    marketData.getEbayMarketData().getLowestAskPrice(),
                    pricingInfo.getCost(),
                    pricingInfo.getProfitRate(),
                    ListingAccount.EBAY_CONSIGNMENT,
                    shopId
            );
            bulkModifyPricingResItem.setEbay(salePrice);
        }

        // 如果有 Poizon 的数据
        if (marketData.getPoizonMarketData() != null && salePlatforms.contains(SourcePlatform.POIZON)) {
            KnetProductPrice salePrice = judgeLowestAskPrice(
                    marketData.getPoizonMarketData().getLowestAskPrice(),
                    pricingInfo.getCost(),
                    pricingInfo.getProfitRate(),
                    ListingAccount.POIZON_CONSIGNMENT,
                    shopId
            );
            bulkModifyPricingResItem.setPoizon(salePrice);
        }

        if (marketData.getTtsMarketData() != null && salePlatforms.contains(SourcePlatform.TIKTOK_SHOP)) {
            KnetProductPrice salePrice = judgeLowestAskPrice(
                    marketData.getTtsMarketData().getLowestAskPrice(),
                    pricingInfo.getCost(),
                    pricingInfo.getProfitRate(),
                    ListingAccount.TIKTOK_SHOP,
                    shopId
            );
            bulkModifyPricingResItem.setTts(salePrice);
        }

        return bulkModifyPricingResItem;
    }

    // 根据成本和利润控制的利率 和 当前产品最低价 和市场最高求购机对比 比较，返回最高的那一个
    private KnetProductPrice judgeLowestAskPrice(KnetProductPrice marketLowestPrice, KnetProductPrice costPrice, String profit, ListingAccount account, Integer shopId) {
        int cost = costPrice.getAmountUsdCents();
        double profitRate = Double.parseDouble(profit);
        int profitControlPrice = (int) (cost * (( 100 + profitRate) / 100));
        KnetProductPrice salePrice = pricingDetailStrategyContext.calculateSalePrice(account, new KnetProductPrice(profitControlPrice), shopId);
        int roundedSalePriceCents = Math.round((salePrice.getAmountUsdCents() + 50) / 100.0f) * 100;
        int finalSalePrice = Math.max(roundedSalePriceCents, marketLowestPrice.getAmountUsdCents());
        return new KnetProductPrice(finalSalePrice);
    }

}
