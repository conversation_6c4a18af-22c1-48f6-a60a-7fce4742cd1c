package com.hzjm.crosslisting.pricing.usecase.revenue.impl;

import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.pricing.common.PlatformFeeInfo;
import com.hzjm.crosslisting.pricing.entity.RevenueDetail;
import com.hzjm.crosslisting.pricing.usecase.revenue.IPricingDetailCalculator;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class PoizonPricingDetailCalculator implements IPricingDetailCalculator {

    /**
     * 根据平台给出的产品市售数据 来计算 到手价以及价格相关的数据
     * @param salePrice 最终出售价
     * @return 价格信息
     */
    @Override
    public RevenueDetail calculateRevenue(SourcePlatform platform,
                                          PlatformFeeInfo userFeeInfo,
                                          KnetProductPrice salePrice) {
        if (ObjectUtils.isEmpty(salePrice) || salePrice.getAmountUsdCents() == 0) {
            return RevenueDetail.zeroDetail(userFeeInfo);
        }

        int amountUSDCents = salePrice.getAmountUsdCents();
        RevenueDetail revenueDetail = new RevenueDetail();
        // 填充手续费率
        revenueDetail.setCommissionRate(userFeeInfo.getCommissionRate());

        // 填充手续费
        int minCommissionFee = userFeeInfo.isMinCommissionFree() ? 0 : 1300;
        double commissionRate = Double.parseDouble(userFeeInfo.getCommissionRate());
        int commissionFee = (int) ((commissionRate / 100) * amountUSDCents);
        commissionFee = Math.max(commissionFee, minCommissionFee); // 手续费最低为 13 美刀
        revenueDetail.setCommissionFee(new KnetProductPrice(commissionFee));

        // 填充资金服务费率
        revenueDetail.setCashOutRate(userFeeInfo.getPaymentProcRate());

        // 填充资金服务费
        double paymentProcRate = Double.parseDouble(userFeeInfo.getPaymentProcRate());
        int paymentProcFee = (int) ((paymentProcRate / 100) * amountUSDCents);
        revenueDetail.setCashOutFee(new KnetProductPrice(paymentProcFee));

        // 填充运费
        revenueDetail.setShippingFee(userFeeInfo.getShippingFee());

        // 填充 Knet 平台手续费
        revenueDetail.setKnetCommissionFee(userFeeInfo.getKnetCommissionFee());

        // 填充 所得 费用
        // 所得费用 = 最终销售价 - 手续费 - 资金服务费 - 运费 - kent 平台抽成
        int owning = amountUSDCents - commissionFee - paymentProcFee - userFeeInfo.getShippingFee().getAmountUsdCents() - userFeeInfo.getKnetCommissionFee().getAmountUsdCents();
        revenueDetail.setOwning(new KnetProductPrice(owning));

        revenueDetail.setPlatform(platform);

        return revenueDetail;
    }

    /**
     *  根据 输入的利润来反推一个应该的售价
     * @return
     */
    @Override
    public KnetProductPrice calculateSalePrice(SourcePlatform platform,
                                                PlatformFeeInfo userFeeInfo,
                                                KnetProductPrice owningPrice) {
        int owningCents = owningPrice.getAmountUsdCents();

        // 获取各项费用
        int shippingFeeCents = userFeeInfo.getShippingFee().getAmountUsdCents();
        int knetCommissionFeeCents = userFeeInfo.getKnetCommissionFee().getAmountUsdCents();

        // 计算手续费率
        double commissionRate = Double.parseDouble(userFeeInfo.getCommissionRate());
        int minCommissionFee = userFeeInfo.isMinCommissionFree() ? 0 : 1300;

        // 计算资金服务费率
        double paymentProcRate = Double.parseDouble(userFeeInfo.getPaymentProcRate());

        // 设立方程 salePrice = owning + commissionFee + paymentProcFee + shippingFee + knetCommissionFee
        // 其中 commissionFee = max((commissionRate / 100) * salePrice, minCommissionFee)
        // paymentProcFee = (paymentProcRate / 100) * salePrice

        // 重新整理方程
        // salePrice = (owning + shippingFee + knetCommissionFee + minCommissionFee) / (1 - commissionRate / 100 - paymentProcRate / 100)
        double commissionRateDecimal = commissionRate / 100;
        double paymentProcRateDecimal = paymentProcRate / 100;
        double totalRate = commissionRateDecimal + paymentProcRateDecimal;

        // 直接通过方程计算 salePrice
        int salePriceCents = (int) ((owningCents + shippingFeeCents + knetCommissionFeeCents + minCommissionFee) / (1 - totalRate));

        // 计算手续费
        int commissionFeeCents = (int) (commissionRateDecimal * salePriceCents);
        commissionFeeCents = Math.max(commissionFeeCents, minCommissionFee);

        // 计算资金服务费
        int paymentProcFeeCents = (int) (paymentProcRateDecimal * salePriceCents);

        // 重新计算 salePriceCents 包含所有费用
        salePriceCents = owningCents + commissionFeeCents + paymentProcFeeCents + shippingFeeCents + knetCommissionFeeCents;

        return new KnetProductPrice(salePriceCents);
    }

}
