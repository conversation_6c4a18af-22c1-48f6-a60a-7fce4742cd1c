package com.hzjm.crosslisting.pricing.usecase.revenue.impl;

import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.pricing.common.PlatformFeeInfo;
import com.hzjm.crosslisting.pricing.entity.RevenueDetail;
import com.hzjm.crosslisting.pricing.usecase.revenue.IPricingDetailCalculator;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class EbayPricingDetailCalculator implements IPricingDetailCalculator {

    // ebay 对 150 美刀以下的鞋子有一个固定的涨幅
    private static final String increaseCommission = "2.0";

    @Override
    public RevenueDetail calculateRevenue(SourcePlatform platform,
                                          PlatformFeeInfo userFeeInfo,
                                          KnetProductPrice salePrice) {

        if (ObjectUtils.isEmpty(salePrice) || salePrice.getAmountUsdCents() == 0) {
            return RevenueDetail.zeroDetail(userFeeInfo);
        }

        int amountUSDCents = salePrice.getAmountUsdCents();
        RevenueDetail revenueDetail = new RevenueDetail();
        int commissionFee;

        // 超过 150 美刀的鞋子 按 基准费率计费
        if (amountUSDCents >= 15000) {
            // 填充手续费率
            revenueDetail.setCommissionRate(userFeeInfo.getCommissionRate());

            // 填充手续费
            int minCommissionFee = userFeeInfo.isMinCommissionFree() ? 0 : 600;
            double commissionRate = Double.parseDouble(userFeeInfo.getCommissionRate());
            commissionFee = (int) ((commissionRate / 100) * amountUSDCents);
            commissionFee = Math.max(commissionFee, minCommissionFee); // 手续费最低为 6 美刀
            revenueDetail.setCommissionFee(new KnetProductPrice(commissionFee));
        }
        // 对于 低于150 美刀的鞋子， 需要新增一个 2% 的费率涨幅
        else {
            // 将字符串转换为 double
            double baseCommissionRate = Double.parseDouble(userFeeInfo.getCommissionRate()); // 基础 费率
            double increaseCommissionRate = Double.parseDouble(increaseCommission);
            // 数值相加
            double sum = baseCommissionRate + increaseCommissionRate;
            // 将结果转换为字符串
            String finalCommissionRate = Double.toString(sum);

            revenueDetail.setCommissionRate(finalCommissionRate);
            int minCommissionFee = userFeeInfo.isMinCommissionFree() ? 0 : 600;
            commissionFee = (int) ((sum / 100) * amountUSDCents);
            commissionFee = Math.max(commissionFee, minCommissionFee);
            revenueDetail.setCommissionFee(new KnetProductPrice(commissionFee));
        }

        // 填充资金服务费率
        revenueDetail.setCashOutRate(userFeeInfo.getPaymentProcRate());

        // 填充资金服务费
        double paymentProcRate = Double.parseDouble(userFeeInfo.getPaymentProcRate());
        int paymentProcFee = (int) ((paymentProcRate / 100) * amountUSDCents);
        revenueDetail.setCashOutFee(new KnetProductPrice(paymentProcFee));

        // 填充运费
        revenueDetail.setShippingFee(userFeeInfo.getShippingFee());

        // 填充 Knet 平台手续费
        revenueDetail.setKnetCommissionFee(userFeeInfo.getKnetCommissionFee());

        // 填充 所得 费用
        // 所得费用 = 最终销售价 - 手续费 - 资金服务费 - 运费 - kent 平台抽成
        int owning = amountUSDCents - commissionFee - paymentProcFee - userFeeInfo.getShippingFee().getAmountUsdCents() - userFeeInfo.getKnetCommissionFee().getAmountUsdCents();
        revenueDetail.setOwning(new KnetProductPrice(owning));

        revenueDetail.setPlatform(platform);

        return revenueDetail;
    }

    @Override
    public KnetProductPrice calculateSalePrice(SourcePlatform platform,
                                               PlatformFeeInfo userFeeInfo,
                                               KnetProductPrice owningPrice) {

        if (ObjectUtils.isEmpty(owningPrice) || owningPrice.getAmountUsdCents() == 0) {
            return new KnetProductPrice(0);
        }

        int owningCents = owningPrice.getAmountUsdCents();
        int shippingFeeCents = userFeeInfo.getShippingFee().getAmountUsdCents();
        int knetCommissionFeeCents = userFeeInfo.getKnetCommissionFee().getAmountUsdCents();

        double commissionRate;
        int minCommissionFee = userFeeInfo.isMinCommissionFree() ? 0 : 600;
        double paymentProcRate = Double.parseDouble(userFeeInfo.getPaymentProcRate());

        // 计算手续费率
        if (owningCents >= 15000) {
            commissionRate = Double.parseDouble(userFeeInfo.getCommissionRate());
        } else {
            double baseCommissionRate = Double.parseDouble(userFeeInfo.getCommissionRate());
            double increaseCommissionRate = Double.parseDouble(increaseCommission);
            commissionRate = baseCommissionRate + increaseCommissionRate;
        }

        // 设立方程 salePrice = owning + commissionFee + paymentProcFee + shippingFee + knetCommissionFee
        // 其中 commissionFee = max((commissionRate / 100) * salePrice, minCommissionFee)
        // paymentProcFee = (paymentProcRate / 100) * (salePrice - commissionFee - shippingFee)

        // 重新整理方程
        // salePrice = owning + max((commissionRate / 100) * salePrice, minCommissionFee) + (paymentProcRate / 100) * (salePrice - max((commissionRate / 100) * salePrice, minCommissionFee) - shippingFee) + shippingFee + knetCommissionFee

        // 使用迭代法求解 salePrice
        double salePriceCents = owningCents + shippingFeeCents + knetCommissionFeeCents;
        double previousSalePriceCents;

        do {
            previousSalePriceCents = salePriceCents;
            double commissionFeeCents = Math.max((commissionRate / 100) * salePriceCents, minCommissionFee);
            double priceWithoutProcFee = salePriceCents - commissionFeeCents - shippingFeeCents;
            double paymentProcFeeCents = (paymentProcRate / 100) * priceWithoutProcFee;
            salePriceCents = owningCents + commissionFeeCents + paymentProcFeeCents + shippingFeeCents + knetCommissionFeeCents;
        } while (Math.abs(salePriceCents - previousSalePriceCents) > 1);

        // 向上进位到最接近的百位数
        int roundedSalePriceCents = (int) (Math.ceil(salePriceCents / 100.0) * 100);

        return new KnetProductPrice(roundedSalePriceCents);
    }

}
