package com.hzjm.crosslisting.pricing.usecase.revenue;

import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.pricing.common.PlatformFeeInfo;
import com.hzjm.crosslisting.pricing.common.PricingFeeConstant;
import com.hzjm.crosslisting.pricing.entity.RevenueDetail;
import com.hzjm.crosslisting.pricing.usecase.revenue.impl.*;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PricingDetailStrategyContext {

    @Autowired
    private GoatPricingDetailCalculator goatPricingCalculator;

    @Autowired
    private StockXPricingDetailCalculator stockXPricingCalculator;

    @Autowired
    private KicksCrewDetailCalculator kicksCrewDetailCalculator;

    @Autowired
    private EbayPricingDetailCalculator ebayPricingDetailCalculator;

    @Autowired
    private PoizonPricingDetailCalculator poizonPricingDetailCalculator;

    @Autowired
    private TTSPricingDetailCalculator ttsPricingDetailCalculator;

    @Autowired
    private B2bPricingDetailCalculator b2BPricingDetailCalculator;

    @Autowired
    private IShopUserFeeInfoFetcher userFeeInfoFetcher;

    public RevenueDetail calculateRevenueDetail(ListingAccount account, KnetProductPrice salePrice, Integer shopId) {
        PlatformFeeInfo feeInfo = userFeeInfoFetcher.fetchFeeInfo(shopId, account);
        switch (account.toSourcePlatform()) {
            case GOAT_STV:
            case GOAT_INSTANT_SHIP:
                return goatPricingCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        feeInfo,
                        salePrice
                );
            case STOCK_X:
                return stockXPricingCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        feeInfo,
                        salePrice
                );
            case KICKS_CREW:
                return kicksCrewDetailCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        feeInfo,
                        salePrice
                );
            case EBAY:
                return ebayPricingDetailCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        feeInfo,
                        salePrice
                );
            case POIZON:
                return poizonPricingDetailCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        feeInfo,
                        salePrice
                );
            case TIKTOK_SHOP:
                return ttsPricingDetailCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        feeInfo,
                        salePrice
                );
            case B2B_SHOP:
                return b2BPricingDetailCalculator.calculateRevenue(
                        account.toSourcePlatform(),
                        knetOfficialAccount(account.toSourcePlatform()),
                        salePrice
                );
            default:
                throw new IllegalArgumentException("Calculate Pricing Detail Error, Unsupported platform: " + account.toSourcePlatform());
        }
    }

    public KnetProductPrice calculateSalePrice(ListingAccount account, KnetProductPrice revenue, Integer shopId) {
        PlatformFeeInfo feeInfo = userFeeInfoFetcher.fetchFeeInfo(shopId, account);
        switch (account.toSourcePlatform()) {
            case GOAT_STV:
            case GOAT_INSTANT_SHIP:
                return goatPricingCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            case STOCK_X:
                return stockXPricingCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            case KICKS_CREW:
                return kicksCrewDetailCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            case EBAY:
                return ebayPricingDetailCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            case POIZON:
                return poizonPricingDetailCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            case TIKTOK_SHOP:
                return ttsPricingDetailCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            case B2B_SHOP:
                return b2BPricingDetailCalculator.calculateSalePrice(
                        account.toSourcePlatform(),
                        feeInfo,
                        revenue
                );
            default:
                throw new IllegalArgumentException("Calculate Sale Price Error, Unsupported platform: " + account.toSourcePlatform());
        }
    }

    public KnetProductPrice revenueAdjustment(SourcePlatform platform, KnetProductPrice knetOwning, KnetProductPrice sellerPrice) {
        switch (platform) {
            case STOCK_X:
                return stockXPricingCalculator.revenueAdjustment(knetOwning, sellerPrice);
            case GOAT_STV:
            case GOAT_INSTANT_SHIP:
                return goatPricingCalculator.revenueAdjustment(knetOwning, sellerPrice);
            case KICKS_CREW:
                return kicksCrewDetailCalculator.revenueAdjustment(knetOwning, sellerPrice);
            case EBAY:
                return ebayPricingDetailCalculator.revenueAdjustment(knetOwning, sellerPrice);
            case POIZON:
                return poizonPricingDetailCalculator.revenueAdjustment(knetOwning, sellerPrice);
            case TIKTOK_SHOP:
                return ttsPricingDetailCalculator.revenueAdjustment(knetOwning, sellerPrice);
            case B2B_SHOP:
                return b2BPricingDetailCalculator.revenueAdjustment(knetOwning, sellerPrice);
            default:
                throw new IllegalArgumentException("Calculate Pricing Detail Error, Unsupported platform: " + platform);
        }
    }

    /**
     * 各平台给 Knet 的 计费信息
     *
     * @param platform
     * @return
     */
    private static PlatformFeeInfo knetOfficialAccount(SourcePlatform platform) {
        switch (platform) {
            case STOCK_X:
                return PricingFeeConstant.STOCKX_TO_KNET_FEE_INFO;
            case GOAT_INSTANT_SHIP:
                return PricingFeeConstant.GOAT_INSTANT_TO_KNET_FEE_INFO;
            case GOAT_STV:
                return PricingFeeConstant.GOAT_STV_TO_KNET_FEE_INFO;
            default:
                throw new IllegalArgumentException("Create UserPricingInfo Error, Unsupported platform: " + platform);
        }
    }

}
