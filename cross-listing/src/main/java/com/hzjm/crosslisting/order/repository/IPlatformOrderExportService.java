package com.hzjm.crosslisting.order.repository;

import com.hzjm.crosslisting.order.data.dto.PlatformOrderExportRequest;
import com.hzjm.crosslisting.order.data.dto.PlatformOrderGroupQueryRequest;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/13 13:33
 * @description: 销售订单导出服务
 */
public interface IPlatformOrderExportService {
    /**
     * 导出销售订单（重写版本-迁移旧方法）
     *
     * @param req      原始请求
     * @param fileName 文件名
     * @return 导出文件地址
     */
    HashMap<String, String> exportPlatformOrder(PlatformOrderExportRequest req, String fileName);

    /**
     * 创建导出销售订单（异步）任务
     *
     * @param request  导出请求
     * @param language 用户使用语言
     * @return 是否成功
     */
    Boolean createExportTask(PlatformOrderGroupQueryRequest request, String language);

    /**
     * 获取文件名
     *
     * @param request 请求
     * @return 文件名
     */
    String getFileName(PlatformOrderExportRequest request);
}
