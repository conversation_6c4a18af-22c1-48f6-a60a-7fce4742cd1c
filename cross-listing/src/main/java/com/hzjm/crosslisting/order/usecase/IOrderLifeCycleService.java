package com.hzjm.crosslisting.order.usecase;

import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.entity.PlatformOrderFilter;
import com.hzjm.crosslisting.order.entity.PlatformOrderLifeCycle;
import com.hzjm.goat.order.dto.GoatOrder;
import com.hzjm.stockx.order.data.StockXOrder;

import java.util.List;

public interface IOrderLifeCycleService {

    List<PlatformOrderLifeCycle> getAnOrderLifeCycle(String orderId);

    void setLifeCycleFor(PlatformOrder order);

    void setLifeCycleFor(List<PlatformOrder> orders);

}
