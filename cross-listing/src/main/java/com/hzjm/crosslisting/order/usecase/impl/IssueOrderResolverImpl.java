package com.hzjm.crosslisting.order.usecase.impl;

import com.hzjm.crosslisting.common.AccountConfig;
import com.hzjm.crosslisting.order.data.vo.IssueOrderDetail;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.usecase.IIssueOrderResolver;
import com.hzjm.goat.order.dto.GoatOrder;
import com.hzjm.goat.order.usecase.IGoatOrderService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IssueOrderResolverImpl implements IIssueOrderResolver {

    @Autowired
    IGoatOrderService goatOrderService;

    @Autowired
    AccountConfig accountConfig;

    /**
     * @param order
     * @return
     */
    @Override
    public IssueOrderDetail getIssueOrderDetail(PlatformOrder order) {
        GoatOrder issueOrder = goatOrderService.getSingleOrderBy(
                order.getOrderNumber(),
                order.getAccount().getAccountEmail(accountConfig)
        );
        return IssueOrderDetail.createFrom(issueOrder);
    }

}

