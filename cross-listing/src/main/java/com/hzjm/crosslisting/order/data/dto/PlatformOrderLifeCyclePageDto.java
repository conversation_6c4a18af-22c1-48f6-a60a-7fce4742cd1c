package com.hzjm.crosslisting.order.data.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

/**
 * -列表查询
 *
 * <AUTHOR>
 * @since 2024-01-08
 */
@Data
public class PlatformOrderLifeCyclePageDto extends PageBaseSearchDto {

}
