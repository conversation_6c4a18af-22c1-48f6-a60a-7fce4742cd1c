package com.hzjm.crosslisting.order.usecase.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.crosslisting.enums.KnetProductListingStatus;
import com.hzjm.crosslisting.enums.ListingStatus;
import com.hzjm.crosslisting.enums.PlatformAutoPricingStatus;
import com.hzjm.crosslisting.enums.PlatformStatus;
import com.hzjm.crosslisting.listing.entity.KnetProductListing;
import com.hzjm.crosslisting.listing.entity.PlatformListing;
import com.hzjm.crosslisting.listing.repository.IKnetProductListingService;
import com.hzjm.crosslisting.listing.repository.IPlatformListingService;
import com.hzjm.crosslisting.listing.usecase.IPlatformListingBatchService;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.repository.IPlatformOrderService;
import com.hzjm.crosslisting.order.usecase.IOversoldOrderReplaceService;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.entity.SysWare;
import com.hzjm.service.entity.SysWareShelves;
import com.hzjm.service.service.ISysProdSearchService;
import com.hzjm.service.service.ISysProdService;
import com.hzjm.service.service.ISysWareService;
import com.hzjm.service.service.ISysWareShelvesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OversoldOrderReplaceServiceImpl implements IOversoldOrderReplaceService {

    @Autowired
    IPlatformListingBatchService platformListingBatchService;

    @Autowired
    IKnetProductListingService knetProductListingService;

    @Autowired
    IPlatformListingService platformListingService;

    @Autowired
    IPlatformOrderService platformOrderService;

    @Autowired
    ISysProdService sysProdService;

    @Autowired
    ISysProdSearchService sysProdSearchService;

    @Autowired
    ISysWareService sysWareService;

    @Autowired
    ISysWareShelvesService sysWareShelvesService;

    /**
     * 替换 订单的 one Id 以及所属数据
     *
     * @param orderNumber   需要替换的 订单号
     * @param replaceOneId  需要替换的 oneId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean replacementOrder(String orderNumber, String replaceOneId, Boolean isOversold) {
        //1. 查找 提交 orderNumber 是否存在对应的订单
        PlatformOrder replaceOrder = platformOrderService.getOne(Wrappers.<PlatformOrder>lambdaQuery().eq(PlatformOrder::getOrderNumber, orderNumber));

        if (replaceOrder == null) {
            throw new BaseException("无法找到需要替换的订单号: " + orderNumber + " 的订单，请检查后再试");
        }

        PlatformListing replaceListing = platformListingService.getOne(Wrappers.<PlatformListing>lambdaQuery()
                .eq(PlatformListing::getPlatformListingId, replaceOrder.getPlatformListingId())
                .last("limit 1")
        );

        if (replaceListing == null) {
            throw new BaseException("无法找到需要替换的订单号: " + orderNumber + " 的 PlatformListing，请检查后再试");
        }

        //2. 获取需要替换 One ID 的所有 库存数据
        SysProd replaceSysProd = sysProdService.getOne(Wrappers.<SysProd>lambdaQuery()
                .eq(SysProd::getOneId, replaceOneId)
                .in(SysProd::getStatus, 1, 2)
                .last("limit 1")
        );

        if (replaceSysProd == null) {
            throw new BaseException("无法找到处在 寄卖中或空闲的 的 One ID: " + replaceOneId + " 的 Sys Prod 据，请检查后再试");
        }

        SysProdSearch replaceSysProdSearch = sysProdSearchService.getOne(Wrappers.<SysProdSearch>lambdaQuery()
                .eq(SysProdSearch::getOneId, replaceOneId)
                .eq(SysProdSearch::getSearchType, 1)
                .last("limit 1")
        );

        if (replaceSysProdSearch == null) {
            throw new BaseException("无法找到需要替换的 One ID: " + replaceOneId + " 的 SysProdSearch数据，请检查后再试");
        }

        // 非超卖替换 的话被替换的商品库存数据需要置为空闲
        SysProd originProd  = sysProdService.getOne(Wrappers.<SysProd>lambdaQuery()
                .eq(SysProd::getOneId, replaceOrder.getOneId())
                .last("limit 1")
        );
        SysProdSearch originProdSearch = sysProdSearchService.getOne(Wrappers.<SysProdSearch>lambdaQuery()
                .eq(SysProdSearch::getOneId, replaceOrder.getOneId())
                .eq(SysProdSearch::getSearchType, 1)
                .last("limit 1")
        );

        if (!(Objects.equals(originProd.getSku(), replaceSysProd.getSku()) && Objects.equals(originProd.getSpec(), replaceSysProd.getSpec()))) {
            throw new BaseException("替换的 One ID: " + replaceOneId + " 的 SKU 和规格与原订单的 SKU 和规格不一致，请检查后再试");
        }

        //3. 检查提交的 oneID 是否能替换
        List<PlatformListing> needCancelOneIdListings = platformListingService.list(Wrappers.<PlatformListing>lambdaQuery()
                .eq(PlatformListing::getOneId, replaceOneId)
        );

        // 提交的 One ID 似乎已经匹配了订单不能再进行替换
        if (needCancelOneIdListings
                .stream()
                .anyMatch(listing ->
                        Objects.equals(listing.getSaleStatus(), ListingStatus.MATCHED)
                                || Objects.equals(listing.getSaleStatus(), ListingStatus.COMPLETED))
        ){
            throw new BaseException("想要替换超卖的 One ID: " + replaceOneId + " 可能已经被卖出，无法进行替换，请检查后再试");
        }

        // 批量下架 匹配的数据
        List<PlatformListing> failedListings = platformListingBatchService.batchCancelListing(needCancelOneIdListings
                .stream()
                .filter(a -> Objects.equals(a.getSaleStatus(), ListingStatus.PENDING)
                        || Objects.equals(a.getSaleStatus(), ListingStatus.ACTIVE)
                )
                .collect(Collectors.toList())
        );
        // 批量下架失败
        if (!failedListings.isEmpty()) {
            throw new BaseException("批量下架 One ID: " + replaceOneId + " 的数据失败，请检查后再试");
        }

        //4. 开始更新替换数据

        //1. 处理 product listing 数据
        KnetProductListing productListing = knetProductListingService.getOne(
                Wrappers.<KnetProductListing>lambdaQuery()
                        .eq(KnetProductListing::getOneId, replaceOneId)
                        .in(KnetProductListing::getKnetListingStatus, KnetProductListingStatus.CREATED, KnetProductListingStatus.UPDATED)
                        .last("limit 1")
        );
        if (ObjectUtils.isNotEmpty(productListing)) {
            productListing.setKnetListingStatus(KnetProductListingStatus.DELETED);
            // 各平台 的 平台 和 自动跟价 开关都设置关闭
            productListing.setStockxStatus(PlatformStatus.CLOSE);
            productListing.setStockxAutoPricingStatus(PlatformAutoPricingStatus.CLOSE);
            productListing.setStockxListingStatus(ListingStatus.INACTIVE);
            productListing.setGoatStatus(PlatformStatus.CLOSE);
            productListing.setGoatAutoPricingStatus(PlatformAutoPricingStatus.CLOSE);
            productListing.setGoatListingStatus(ListingStatus.INACTIVE);
            productListing.setGoatisStatus(PlatformStatus.CLOSE);
            productListing.setGoatisAutoPricingStatus(PlatformAutoPricingStatus.CLOSE);
            productListing.setGoatisListingStatus(ListingStatus.INACTIVE);
            productListing.setKcStatus(PlatformStatus.CLOSE);
            productListing.setKcAutoPricingStatus(PlatformAutoPricingStatus.CLOSE);
            productListing.setKcListingStatus(ListingStatus.INACTIVE);
            productListing.setEbayStatus(PlatformStatus.CLOSE);
            productListing.setEbayAutoPricingStatus(PlatformAutoPricingStatus.CLOSE);
            productListing.setEbayListingStatus(ListingStatus.INACTIVE);
            productListing.setPoizonStatus(PlatformStatus.CLOSE);
            productListing.setPoizonAutoPricingStatus(PlatformAutoPricingStatus.DISABLED);
            productListing.setPoizonListingStatus(ListingStatus.INACTIVE);
            productListing.setTtsStatus(PlatformStatus.CLOSE);
            productListing.setTtsAutoPricingStatus(PlatformAutoPricingStatus.DISABLED);
            productListing.setTtsListingStatus(ListingStatus.INACTIVE);
        }

        SysWare wareInfo = sysWareService.getOne(Wrappers.<SysWare>lambdaQuery().eq(SysWare::getId, replaceSysProd.getWareId()).last("limit 1"));
        if (ObjectUtils.isEmpty(wareInfo)) {
            throw new BaseException("缺少 WareId: " + replaceSysProd.getWareId() + " 的 Ware 数据，请检查后再试");
        }

        SysWareShelves shelves = sysWareShelvesService.getOne(Wrappers.<SysWareShelves>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(replaceSysProdSearch.getShelvesId()),SysWareShelves::getId, replaceSysProdSearch.getShelvesId())
                .last("limit 1")
        );

        // 处理需要替换的 Platform Listing 数据
        Integer userId = replaceSysProdSearch.getShopId();
        replaceListing.setShopUserId(String.valueOf(userId));
        replaceListing.setOneId(replaceOneId);

        // 处理需要替换的 order 数据
        replaceOrder.setOneId(replaceOneId);
        replaceOrder.setShopId(String.valueOf(userId));
        replaceOrder.setWareId(String.valueOf(replaceSysProd.getWareId()));
        replaceOrder.setWareName(wareInfo.getName());
        replaceOrder.setShelvesId(ObjectUtils.isEmpty(shelves) ? "" : String.valueOf(shelves.getId()));
        replaceOrder.setShelvesName(ObjectUtils.isEmpty(shelves) ? "" : shelves.getName());
        replaceOrder.setRemark("");

        // 设置这些数据正在寄卖中
        replaceSysProd.setStatus(2);
        replaceSysProdSearch.setStatus(2);

        if (!isOversold && ObjectUtils.isNotEmpty(originProd) && ObjectUtils.isNotEmpty(originProdSearch)) {
            // 非超卖替换的话需要将被替换的商品库存数据置为空闲
            originProd.setStatus(1);
            originProdSearch.setStatus(1);
            sysProdService.updateById(originProd);
            sysProdSearchService.updateById(originProdSearch);
        }

        //5. 更新数据
        sysProdService.updateById(replaceSysProd);
        sysProdSearchService.updateById(replaceSysProdSearch);
        if (ObjectUtils.isNotEmpty(productListing)) {
            knetProductListingService.updateById(productListing);
        }
        platformListingService.updateById(replaceListing);
        platformOrderService.updateById(replaceOrder);

        return true;
    }


}
