package com.hzjm.crosslisting.order.usecase.impl;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.crosslisting.common.AccountConfig;
import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.PlatformOrderStatus;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.repository.IPlatformOrderService;
import com.hzjm.service.entity.*;
import com.hzjm.service.service.*;
import com.hzjm.tts.order.data.OrderReturnStatus;
import com.hzjm.tts.order.data.SimplifyReturnInfo;
import com.hzjm.tts.order.data.request.SearchReturnsRequest;
import com.hzjm.tts.order.data.response.TTSOrderRevenueResponse;
import com.hzjm.tts.order.data.response.TTSReturns;
import com.hzjm.tts.order.usecase.ITTSOrderUseCase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TTSReturnOrderProcessor {

    @Autowired
    IShopPreService shopPreService;

    @Autowired
    IShopPackService shopPackService;

    @Autowired
    ITTSOrderUseCase ttsOrderUseCase;

    @Autowired
    AccountConfig accountConfig;

    @Autowired
    IPlatformOrderService platformOrderService;

    @Resource
    ISysProdService sysProdService;

    @Resource
    ISysProdDealService sysProdDealService;

    //UID: TTSR  TTS 退货的专用账号
    private static final Integer TTS_RETURN_USER_ID = 1540;

    /**
     * 获取当前所有 Return 的 order
     * @return
     */
    public List<PlatformOrder> fetchReturnsOrder() {
        SearchReturnsRequest returnsRequest = new SearchReturnsRequest();
        // 计算一个月前前的 Unix timestamp
        long now = System.currentTimeMillis() / 1000L;
        long oneMonthTimestamp = now  - (50 * 24 * 60 * 60);
        // 设置初始的 update_time_ge 属性
        returnsRequest.setCreate_time_ge((int) oneMonthTimestamp);
        returnsRequest.setCreate_time_lt((int) now);

        // 设置需要搜索 的 returns 状态
        List<OrderReturnStatus> trackingStatusList = new ArrayList<>();
        trackingStatusList.add(OrderReturnStatus.RETURN_OR_REFUND_REQUEST_COMPLETE);
        trackingStatusList.add(OrderReturnStatus.BUYER_SHIPPED_ITEM);
        returnsRequest.setReturn_status(trackingStatusList);

        // 获取 所有 returns
        List<TTSReturns> ttsReturns = new ArrayList<>();
        try {
            ttsReturns = ttsOrderUseCase.getReturnList(returnsRequest, ListingAccount.TIKTOK_SHOP.getAccountEmail(accountConfig));
        }catch (Exception e) {
            log.error("Get return list error: {}", e.getMessage());
        }

        // 无任何需要处理的 returns
        if (ObjectUtils.isEmpty(ttsReturns)) {
            log.info("No tts return orders found.");
            return new ArrayList<>();
        }

        // 按 OrderNumber 分组
        Map<String, SimplifyReturnInfo> returnInfoMap = ttsReturns.stream()
                .flatMap(returnOrder -> returnOrder.getReturn_line_items().stream().map(lineItem -> {
                            try {
                                // 处理每个订单的每个行项目
                                SimplifyReturnInfo returnInfo = new SimplifyReturnInfo();
                                returnInfo.setOrderId(returnOrder.getOrder_id());
                                returnInfo.setOrderNumber(lineItem.getOrder_line_item_id());
                                returnInfo.setOrderReturnStatus(returnOrder.getReturn_status());
                                returnInfo.setCancelReason(returnOrder.getReturn_reason_text());
                                returnInfo.setReturnTrackingNumber(returnOrder.getReturn_tracking_number());

                                return returnInfo;
                            } catch (Exception e) {
                                log.error("Failed to create SimplifyReturnInfo from orderNumber, lineItem: {}, because of: {}", lineItem, e);
                                return null;
                            }
                })
                .filter(Objects::nonNull)
        )
                .collect(Collectors.toMap(SimplifyReturnInfo::getOrderNumber, returnInfo -> returnInfo));


        // 按 orderNumber 查询数据库中的现有订单
        List<PlatformOrder> payoutCompletedOrders = platformOrderService.list(
                        Wrappers.<PlatformOrder>lambdaQuery()
                                .in(PlatformOrder::getOrderNumber, returnInfoMap.keySet())
                                .in(PlatformOrder::getKnetOrderStatus, PlatformOrderStatus.PAYOUT_COMPLETED, PlatformOrderStatus.RETURNING)
                );

        List<PlatformOrder> needProcessOrders = new ArrayList<>();

        // 根据远程符合条件的 order 来逐个 更新 本地已经打款的 订单状态
        payoutCompletedOrders.forEach(paidOrder -> {
            // 获取对应的 returnInfo
            SimplifyReturnInfo returnInfo = returnInfoMap.get(paidOrder.getOrderNumber());
            if (ObjectUtils.isEmpty(returnInfo)) {
                log.error("No return info found for orderNumber: {}", paidOrder.getOrderNumber());
                return;
            }

            if (Objects.equals(returnInfo.getOrderReturnStatus(), OrderReturnStatus.RETURN_OR_REFUND_REQUEST_COMPLETE)) {
                paidOrder.setOrderStatus(PlatformOrderStatus.COMPLETED_WITH_REFUND);
            }else if (Objects.equals(returnInfo.getOrderReturnStatus(), OrderReturnStatus.BUYER_SHIPPED_ITEM)) {
                paidOrder.setOrderStatus(PlatformOrderStatus.RETURNING);
            }
            paidOrder.setTrackingCode(returnInfo.getReturnTrackingNumber());
            paidOrder.setNote(returnInfo.getCancelReason());
            needProcessOrders.add(paidOrder);
        });

        return needProcessOrders;
    }

    /**
     *  tts return 订单入库预报
     */
    @Transactional(rollbackFor = Exception.class)
    public void ttsReturnOrderPre(PlatformOrder order) {
        // 1. 需要预报的 tts 订单 拉取 return order 的单子 的运单号
        String logNo = order.getTrackingCode();
        String logNoSuffix = logNo.substring(logNo.length() - 9);
        ShopPack pack = shopPackService.getOne(Wrappers.<ShopPack>lambdaQuery().ne(ShopPack::getStatus, 3)
                .eq(ShopPack::getLogNoSuffix, logNoSuffix));

        // ShopPack 已经存在,抛出错误返回，即已经被推送过
        if (ObjectUtils.isNotEmpty(pack)) {
            log.info("ShopPack already exists, tracking code: {}, one id: {}, order number: {}",
                    order.getTrackingCode(),
                    order.getOneId(),
                    order.getOrderNumber()
            );
            order.setKnetOrderStatus(PlatformOrderStatus.RETURNING);
            platformOrderService.updateById(order);
            return;
        }

        // 2. 设置预报包裹数据, 静默生成预报批次以及相应包裹
        // 生成 ShopPackProd 包裹
        ShopPackProd prod = new ShopPackProd();
        prod.setSpec(order.getSize());
        prod.setSku(order.getSku());
        prod.setReturnedReason(order.getNote());
        prod.setSupply("TTS Return");
        List<ShopPackProd> prodList = new ArrayList<>();
        prodList.add(prod);

        // Pack
        pack = new ShopPack();
        pack.setShopId(TTS_RETURN_USER_ID);
        pack.setLogNo(logNo);
        pack.setLogNoSuffix(logNoSuffix);
        pack.setReason(order.getNote());
        pack.setNum(1);
        pack.setType(3); // RETURN 类型
        pack.setStatus(1); // 已预约
        pack.setProdList(prodList);

        // 获取 user 的 UUID
        ShopPre pre = new ShopPre();
        pre.setShopId(TTS_RETURN_USER_ID);
        pre.setType(pack.getType());
        pre.setPackList(new ArrayList<>(Collections.singletonList(pack)));
        pre.setNote("TTS Returned Order"
                + ": Origin One Id: "
                + order.getOneId()
        );
        shopPreService.saveShopPre(pre);

        // 5. 更新订单状态为 RETURNING
        order.setKnetOrderStatus(PlatformOrderStatus.RETURNING);
        platformOrderService.updateById(order);
    }

    /**
     *  刷新订单的knet Owning
     * @param order
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderKnetOwning(PlatformOrder order, boolean isRefund) {
        // 计算 knet Owning
        TTSOrderRevenueResponse revenueResponse = ttsOrderUseCase.getOrderSettlement(order.getOrderId(), order.getAccount().getAccountEmail(accountConfig));
        int finalSettlement = revenueResponse.getSku_transactions()
                .stream()
                .filter(skuTransaction -> Objects.equals(skuTransaction.getSku_id(), order.getVariantId()))
                .mapToInt(skuTransaction -> {
                    // 获取 settlement_amount 字符串
                    String settlementAmountStr = skuTransaction.getSettlement_amount();
                    // 将字符串转换为 double，然后乘以 100，并转换为 int
                    // 可以添加额外检查以确保字符串有效
                    return (int) (Double.parseDouble(settlementAmountStr) * 100);
                })
                .sum(); // 计算总和
        order.setKnetOwning(finalSettlement);
        if (isRefund) {
            order.setKnetOrderStatus(PlatformOrderStatus.COMPLETED_WITH_REFUND);
        }

        platformOrderService.updateById(order);
    }

}
