package com.hzjm.crosslisting.order.repository.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.crosslisting.common.AccountConfig;
import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.PlatformOrderStatus;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.order.data.dto.UpdateOrderStatusReq;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.entity.PlatformOrderFilter;
import com.hzjm.crosslisting.order.repository.IPlatformOrderService;
import com.hzjm.crosslisting.order.repository.IRemoteOrderRepository;
import com.hzjm.crosslisting.utils.StringProcessedUtils;
import com.hzjm.stockx.infrastructure.StockXPageResponse;
import com.hzjm.stockx.order.data.StockXActivateOrderRequest;
import com.hzjm.stockx.order.data.StockXHistoricalOrderRequest;
import com.hzjm.stockx.order.data.StockXOrder;
import com.hzjm.stockx.order.usecase.IStockXOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RemoteOrderRepositoryStockXImpl implements IRemoteOrderRepository {

    @Autowired
    IStockXOrderService stockXOrderService;

    @Autowired
    IPlatformOrderService platformOrderService;

    @Autowired
    AccountConfig accountConfig;

    /**
     *  获取所有订单
     * @param account 对应的平台
     * @param filter 过滤条件，例如：获取所有需要发货的， 获取所有已经支付的订单，获取所有有问题的订单
     * @return
     */
    @Override
    public List<PlatformOrder> getAllOrdersBy(ListingAccount account, PlatformOrderFilter filter) {

        List<PlatformOrder> finalOrders;

        // 生成请求 需要的请求头
        StockXActivateOrderRequest activeParameters = new StockXActivateOrderRequest();
        activeParameters.setPageNumber(100);
        StockXHistoricalOrderRequest historyParameters = new StockXHistoricalOrderRequest();
        historyParameters.setPageNumber(100);

        // 根据筛选条件去设置对应的请求头， 不同状态的订单是要从 Active 和 History 的接口里拉取的
        if (filter.isRequestStockXOrderHistoryAPI()) {
            historyParameters.setOrderStatus(filter.toStockXStatusFilter());
        } else {
            activeParameters.setOrderStatus(filter.toStockXStatusFilter());
        }

        List<StockXOrder> allData = new ArrayList<>();
        int page = 1;  // Assume the page starts from 1

        while (true) {
            try {
                // 设置对应的请求头页数
                if (filter.isRequestStockXOrderHistoryAPI()) {
                    historyParameters.setPageNumber(page);
                } else {
                    activeParameters.setPageNumber(page);
                }

                // 根据 不同的过滤条件去请求不同的 stockX 接口
                StockXPageResponse<StockXOrder> response = filter.isRequestStockXOrderHistoryAPI()
                        ? stockXOrderService.getHistoricalOrder(historyParameters, account.getAccountEmail(accountConfig))
                        : stockXOrderService.getActiveOrder(activeParameters, account.getAccountEmail(accountConfig));

                // 获取当前页的所有数据
                List<StockXOrder> pageOrders = response.getData();
                allData.addAll(pageOrders);

                // 如果请求的是历史订单数据，检查获取的数据中createAt属性最早的是否在一周前
                if (filter.isRequestStockXOrderHistoryAPI()) {
                    // 一月前的时间戳
                    Date oneWeekAgo = DateTimeUtils.getOneMonthAgo(DateTimeUtils.getNow());
                    // 使用Stream找出最早的日期
                    Optional<Date> earliestOrderDate = pageOrders.stream()
                            .map(order -> StringProcessedUtils.convertISO8601StringToDate(order.getCreatedAt()))
                            .min(Comparator.naturalOrder());
                    if (earliestOrderDate.isPresent() && earliestOrderDate.get().before(oneWeekAgo)) {
                        break; // 如果存在一周前的数据，跳出循环
                    }
                }

                if (!response.getHasNextPage()) {
                    break;
                }

                page++;
            } catch (Exception e) {
                String errorInfo = "Error occurred while fetching " + filter + " order data from stockX: " + e + ", stack trace: " + BaseUtils.getCallStack();
                log.error(errorInfo);
                break;
            }
        }

        // 如果是待付款还有待发货的订单还需要进一步请求数据
        // 批量接口拉回来的 order 数据不全我们需要再循环请求单个的接口请求一次数据
        finalOrders = getAllOrdersDetail(allData, filter, account);

        log.info("Fetch " + finalOrders.size() + filter.rawValue + "Orders, from " + account.getAccountEmail(accountConfig));

        return finalOrders;
    }

    // 因为 stockX 的批量拉取接口的订单数据不包含 付款信息，因而我们还需要额外的循环请求单一接口获取更详细的付款信息
    private List<PlatformOrder> getAllOrdersDetail(List<StockXOrder> stockXOrders, PlatformOrderFilter filter, ListingAccount account) {
        List<PlatformOrder> platformOrders = new ArrayList<>();
        List<String> orderNumbers = new ArrayList<>();

        switch (filter) {
            case WITH_ISSUE:
                List<String> issueOrderNumbers = stockXOrders
                        .stream()
                        .map(StockXOrder::getOrderNumber)
                        .filter(Objects::nonNull) // 过滤掉 orderNumber 为 null 的选项
                        .collect(Collectors.toList());

                if (!issueOrderNumbers.isEmpty()) {
                    orderNumbers = platformOrderService.list(Wrappers
                                    .<PlatformOrder>lambdaQuery()
                                    .in(PlatformOrder::getOrderNumber, issueOrderNumbers)
                                    .ne(PlatformOrder::getKnetOrderStatus, PlatformOrderStatus.RETURNED)
                            )
                            .stream()
                            .map(PlatformOrder::getOrderNumber)
                            .collect(Collectors.toList());
                }
                platformOrders.addAll(fetchOrderDetails(orderNumbers, account));

                break;
            case PAYOUT_COMPLETED:
            case COMPLETED:
                // 付款完成的订单， 只去拉取 非 付款完成的数据来减少调用
                List<String> needToPayoutOrderNumbers = stockXOrders
                        .stream()
                        .map(StockXOrder::getOrderNumber)
                        .collect(Collectors.toList());

                if (!needToPayoutOrderNumbers.isEmpty()) {
                    orderNumbers = platformOrderService.list(Wrappers
                                    .<PlatformOrder>lambdaQuery()
                                    .in(PlatformOrder::getOrderNumber, needToPayoutOrderNumbers)
                                    .eq(PlatformOrder::getKnetOrderStatus, PlatformOrderStatus.BOXED)
                                    .ne(PlatformOrder::getKnetOrderStatus, PlatformOrderStatus.PAYOUT_COMPLETED)
                            )
                            .stream()
                            .map(PlatformOrder::getOrderNumber)
                            .collect(Collectors.toList());
                }
                platformOrders.addAll(fetchOrderDetails(orderNumbers, account));

                break;
            case NEED_TO_SHIP:
                // 需要发货的鞋子只去更新拉取数据库没有的鞋子的最新数据
                List<String> needToShipStockXOrderNumbers = stockXOrders
                        .stream()
                        .map(StockXOrder::getOrderNumber)
                        .filter(Objects::nonNull) // 过滤掉 orderNumber 为 null 的选项
                        .collect(Collectors.toList());

                if (!needToShipStockXOrderNumbers.isEmpty()) {

                    List<PlatformOrder> existedOrders = Optional.ofNullable(
                                    platformOrderService.list(
                                            Wrappers.<PlatformOrder>lambdaQuery()
                                                    .in(PlatformOrder::getOrderNumber, needToShipStockXOrderNumbers)
                                                    .isNotNull(PlatformOrder::getOneId)
                                                    .isNotNull(PlatformOrder::getKnetOwning)
                                    )
                            )
                            .orElse(Collections.emptyList());

                    // 只添加 为处理的 orders
                    platformOrders.addAll(existedOrders
                            .stream()
                            .filter(a -> !Objects.equals(a.getKnetOrderStatus(), PlatformOrderStatus.BOXED))
                            .collect(Collectors.toList())
                    );

                    // 已经存在 Platform OrderNumber
                    List<String> existedOrderNumbers = existedOrders
                            .stream()
                            .map(PlatformOrder::getOrderNumber)
                            .collect(Collectors.toList());

                    orderNumbers = needToShipStockXOrderNumbers
                            .stream()
                            .filter(orderNumber -> !existedOrderNumbers.contains(orderNumber))
                            .collect(Collectors.toList());

                    platformOrders.addAll(fetchOrderDetails(orderNumbers, account));
                }
                break;
            default:
                break;
        }


        return platformOrders;
    }

    private List<PlatformOrder> fetchOrderDetails(List<String> orderNumbers, ListingAccount account) {
        List<PlatformOrder> remoteOrders = new ArrayList<>();
        for (String orderNumber: orderNumbers) {
            try {
                PlatformOrder platformOrder = getSingleOrder(orderNumber, account);
                remoteOrders.add(platformOrder);
            }catch (Exception e) {
                String info = "从 StockX 进一步拉取订单信息时出错，OrderNumber: "
                        + orderNumber
                        + ", message: "
                        + e + ", stack trace: " + BaseUtils.getCallStack();
                log.error(info);
            }
        }
        return remoteOrders;
    }

    /**
     * 根据 OrderNumber 获取一个订单的详细信息
     * @param orderNumber
     * @return
     */
    @Override
    public PlatformOrder getSingleOrder(String orderNumber, ListingAccount account) {
        StockXOrder order = stockXOrderService.getSingleOrder(orderNumber, account.getAccountEmail(accountConfig));
        return PlatformOrder.createFrom(order, account);
    }

    /**
     * 更新订单的状态，这个方法只能在 Goat 平台使用
     * @param updateOrderInfoList 需要更新的 订单状态信息
     * @return
     */
    @Override
    public String updateOrderStatus(List<UpdateOrderStatusReq> updateOrderInfoList) {
        throw new BaseException("StockX API has no implementation of Update Order Status");
    }

    /**
     * 更新订单的状态， 这个方法只能在 Goat 平台使用
     *
     * @param updateOrderStatusReq 需要被更新的订单的状态信息
     * @return
     */
    @Override
    public String updateOrderStatus(UpdateOrderStatusReq updateOrderStatusReq) {
        throw new BaseException("StockX API has no implementation of Update Order Status");
    }

}
