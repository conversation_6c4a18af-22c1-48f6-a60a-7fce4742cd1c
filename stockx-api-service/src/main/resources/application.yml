#spring
spring:
  main:
    allow-circular-references: true

  # 彩色日志支持打印 ,不支持打印黑白
  output:
    ansi:
      enabled: DETECT

  # 文件传输设置
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 100MB

  # 时间格式化输出
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  #> druid数据源配置
  datasource:
    name: druidDataSource
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***********************************************************************************************************************************************************************************************
      username: jmkj_admin
      password: Mk)Gj(5/xk22DatSd

      #配置监控统计拦截的filters，去掉后监控界面sql将无法统计,'wall'用于防火墙  #,wall,slf4j,config
      filters: stat
      #最大活跃数
      max-active: 100
      #初始化数量
      initial-size: 1
      #最大连接等待超时时间
      max-wait: 60000
      min-idle: 1
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      #打开PSCache，并且指定每个连接PSCache的大小
      pool-prepared-statements: true
      max-open-prepared-statements: 50
      max-pool-prepared-statement-per-connection-size: 20
      #通过connectionProperties属性来打开mergeSql功能；慢SQL记录
      #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  redis:
    host: ***********
    port: 6379
    password:
    timeout: 5000
    lettuce:
      pool:
        min-idle: 1
        max-idle: 10
        max-active: 100
        max-wait: -1ms

#mybatis plus
mybatis-plus:
  global-config:
    db-column-underline: true
    db-config:
      #      id-type: id_worker
      logic-delete-value: -1
      logic-not-delete-value: 0
  configuration:
    map-underscore-to-camel-case: true
  # 扫描 mapper.xml
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  # 别名包扫描路径
  type-aliases-package: com.hzjm.**.entity

#log
logging:
  path: ./logs/${spring.application.name}
  level:
    root: info
    com:
      hzjm:
        service:
          mapper: debug

  file:
    max-history: 30
    max-size: 100MB

