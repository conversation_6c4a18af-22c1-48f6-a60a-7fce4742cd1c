package com.hzjm.stockx.listing.data.dto.operation;

import lombok.Data;

/*
* {
"active": true,
"askData": {
"amount": "100",
"currency": "USD",
"expiresAt": "2022-08-24T18:06:43.600Z"
}
},
*/
@Data
public class StockXOperationAdditionChanges {
    boolean active;
    AskData askData;

    @Data
    public static class AskData {
        String amount;
        String currency;
        String expiresAt;
    }
}
