package com.hzjm.stockx.listing.data.dto.batch;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * {
 * "items": [
 * {
 * "itemId": "98e2e748-8000-45bf-a624-5531d6a68318",
 * "status": "QUEUED",
 * "listingInput": {
 * "currencyCode": "USD",
 * "expiresAt": "2021-11-09T12:44:31.000Z",
 * "amount": "98e2e748-8000-45bf-a624-5531d6a68318",
 * "listingId": "98e2e748-8000-45bf-a624-5531d6a68318"
 * },
 * "result": {
 * "askId": "98e2e748-8000-45bf-a624-5531d6a68318",
 * "listingId": "98e2e748-8000-45bf-a624-5531d6a68318"
 * },
 * "error": "string"
 * }
 * ]
 * }
 */
@Data
@AllArgsConstructor
public class BatchListingUpdateGetItemsResponse {

    List<BatchListingUpdateItem> items;

    @Data
    public static class BatchListingUpdateItem {

        String itemId;
        String status;
        BatchListingUpdateInput listingInput;
        BatchListingUpdateResult result;
        String error;

        @Data
        public static class BatchListingUpdateInput {
            String currencyCode;
            String expiresAt;
            String amount;
            String listingId;
        }

        @Data
        public static class BatchListingUpdateResult {
            String askId;
            String listingId;
        }

    }
}
