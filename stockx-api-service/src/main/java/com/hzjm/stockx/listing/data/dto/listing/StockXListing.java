package com.hzjm.stockx.listing.data.dto.listing;

import com.hzjm.stockx.data.StockXCurrencyCode;
import com.hzjm.stockx.listing.data.dto.operation.StockXListingOperation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/* Listing Response
*{
"listingId": "98e2e748-8000-45bf-a624-5531d6a68318",
"status": "ACTIVE",
"amount": "300",
"currencyCode": "AUD",
"inventoryType": "STANDARD",
"createdAt": "2021-11-09T12:44:31.000Z",
"updatedAt": "2021-11-09T12:44:31.000Z",
"batch": {
"batchId": "86378f62-ad0e-4a06-9c8e-642731bb9140",
"taskId": "7083634e-3bc5-4747-a4f3-768093074b5e"
},
"ask": {
"askId": "string",
"askCreatedAt": "2021-11-09T12:44:31.000Z",
"askUpdatedAt": "2021-11-09T12:44:31.000Z",
"askExpiresAt": "2021-11-09T12:44:31.000Z"
},
"order": {
"orderNumber": "323314425-323214184",
"orderCreatedAt": "2021-11-09T12:44:31.000Z",
"orderStatus": "CREATED"
},
"product": {
"productId": "bf364c53-eb77-4522-955c-6a6ce952cc6f",
"productName": "Nike Air"
},
"variant": {
"variantId": "bf364c53-eb77-4522-955c-6a6ce952cc6f",
"variantName": "color",
"variantValue": "black"
},
"payout": {
"totalPayout": 76.81,
"salePrice": 79,
"totalAdjustments": -7,
"currencyCode": "string",
"adjustments": []
},
"lastOperation": {
"operationId": "bf364c53-eb77-4522-955c-6a6ce952cc6f",
"operationType": "CREATE",
"operationStatus": "PENDING",
"operationInitiatedBy": "USER",
"operationInitiatedVia": "IOS",
"operationCreatedAt": "2021-11-09T12:44:31.000Z",
"operationUpdatedAt": "2021-11-09T12:44:31.000Z",
"changes": {},
"error": ""
}
}
*/
@Data
public class StockXListing {

    @ApiModelProperty(value = "listing ID", required = true)
    String listingId;

    @ApiModelProperty(value = "listing 状态: 'INACTIVE', 'ACTIVE', 'DELETED', 'CANCELED', 'MATCHED', 'COMPLETED'", required = true)
    StockXListingStatus status;

    @ApiModelProperty(value = "售卖价格", required = true)
    String amount;

    @ApiModelProperty("货币符号，不提供默认为USD :'AUD', 'CAD', 'CHF', 'EUR', 'GBP', 'HKD', 'JPY', 'KRW', 'MXN', 'NZD', 'SGD', 'USD'")
    StockXCurrencyCode currencyCode;

    @ApiModelProperty("持有人")
    InventoryType inventoryType;

    @ApiModelProperty(value = "创建时间", required = true)
    String createdAt;

    @ApiModelProperty(value = "更新时间", required = true)
    String updateAt;

    @ApiModelProperty("批量")
    Batch batch;

    @ApiModelProperty("Ask")
    Ask ask;

    @ApiModelProperty("订单")
    Order order;

    @ApiModelProperty("产品")
    Product product;

    @ApiModelProperty("产品变体")
    Variant variant;

    @ApiModelProperty("付款详情")
    StockXPayout payout;

    @ApiModelProperty("最后操作")
    StockXListingOperation lastOperation;

    @ApiModelProperty("入库信息")
    InitiatedShipments initiatedShipments;

    @Data
    public static class Batch {

        @ApiModelProperty(value = "此Listing 关联的 Batch ID", required = true)
        String batchId;


        @ApiModelProperty(value = "此Listing 关联的 task ID", required = true)
        String taskId;
    }

    @Data
    public static class Ask {

        @ApiModelProperty(value = "ask ID", required = true)
        String askId;

        @ApiModelProperty(value = "ask 创建时间", required = true)
        String askCreatedAt;

        @ApiModelProperty(value = "ask 更新时间", required = true)
        String askUpdatedAt;

        @ApiModelProperty(value = "ask 过期时间", required = true)
        String askExpiresAt;
    }

    @Data
    public static class Order {

        @ApiModelProperty("订单号")
        String orderNumber;

        @ApiModelProperty("订单创建日")
        String orderCreatedAt;

        @ApiModelProperty("订单状态")
        String orderStatus;
    }

    @Data
    public static class Product {

        @ApiModelProperty(value = "唯一产品 ID", required = true)
        String productId;

        @ApiModelProperty("产品名")
        String productName;
    }

    @Data
    public static class Variant {

        @ApiModelProperty(value = "产品特殊变体的唯一ID", required = true)
        String variantId;

        @ApiModelProperty(value = "产品特殊变体的可读名", required = true)
        String variantName;

        @ApiModelProperty("变体的值。例如，在运动鞋盒中，这可能是 10，或者在交易卡盒中，这可能是 PSA 10，等等")
        String variantValue;
    }

    @Data
    public static class InitiatedShipments {

        @ApiModelProperty("stockX 入库信息")
        Inbound inbound;

        @Data
        public static class Inbound {
            String displayId;
        }
    }

}
