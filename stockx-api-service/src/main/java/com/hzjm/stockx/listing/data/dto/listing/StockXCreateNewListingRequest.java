package com.hzjm.stockx.listing.data.dto.listing;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;

@Data
@AllArgsConstructor
public class StockXCreateNewListingRequest {

    @NonNull
    @ApiModelProperty(value = "需要售卖的商品的价格", required = true)
    String amount;

    @NonNull
    @ApiModelProperty(value = "需要售卖商品的唯一变体Id", required = true)
    String variantId;

    @ApiModelProperty("该产品列出的货币代码。如果不提供，它将默认为美元。只有stockx.com上支持的有效货币才能通过API支持")
    String currencyCode;

    @ApiModelProperty("UTC时间戳表示此询问应何时自动过期。如果不提供，它将默认为从今天起365天。以ISO 8601格式表示，如2021-11-09T12:44:31.000Z")
    String expiresAt;

    @ApiModelProperty("默认为true的标志，激活StockX市场上的上市")
    boolean active;
}
