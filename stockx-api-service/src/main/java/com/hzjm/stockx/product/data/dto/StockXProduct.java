package com.hzjm.stockx.product.data.dto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;


/**
 * e.g:
 * {
 * "productId": "bf364c53-eb77-4522-955c-6a6ce952cc6f",
 * "urlKey": "purple-hand-bag-leather",
 * "styleId": "BY9109",
 * "productType": "handbags",
 * "title": "Gucci Duchessa Boston Bag",
 * "brand": "Nike",
 * "productAttributes": {
 * "gender": "women",
 * "season": "SS21",
 * "releaseDate": "2017-09-14",
 * "retailPrice": 456,
 * "colorway": "String/Black-Villain Red-Neptune Green",
 * "color": "purple"
 * }
 * }
 */
@Data
@AllArgsConstructor
public class StockXProduct {

    @NonNull
    @ApiModelProperty(value = "产品的唯一标识符", required = true)
    String productId;

    @NonNull
    @ApiModelProperty(value = "产品名称", required = true)
    String urlKey;

    @NonNull
    @ApiModelProperty("产品风格的ID, 这个就是 StockX 平台上真正的的SKU")
    String styleId;

    @ApiModelProperty(value = "StockX上可用的产品类别类型", required = true)
    String productType;

    @ApiModelProperty("产品标题")
    String title;

    @ApiModelProperty("产品品牌")
    String brand;

    @NonNull
    @ApiModelProperty(value = "产品属性", required = true)
    ProductAttributes productAttributes;

    @Data
    public static class ProductAttributes {

        @ApiModelProperty("产品性别")
        String gender;

        @ApiModelProperty("产品发售季节")
        String season;

        @ApiModelProperty("产品发售日")
        String releaseDate;

        @ApiModelProperty("标价")
        String retailPrice;

        @ApiModelProperty("颜色风格")
        String colorway;

        @ApiModelProperty("颜色")
        String color;
    }



}
