package com.hzjm.stockx.product.data.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Data
public class StockXSearchRequest {

    @NonNull
    @ApiModelProperty(value = "需要搜索的产品的关键词，最好是SKU")
    String query;

    int pageNumber = 1;

    int pageSize = 25;

    public StockXSearchRequest(@NonNull String query) {
        this.query = query;
    }

    public StockXSearchRequest(@NonNull String query, int pageNumber) {
        this.query = query;
        this.pageNumber = pageNumber;
    }

    public StockXSearchRequest(@NonNull String query, int pageNumber, int pageSize) {
        this.query = query;
        this.pageNumber = pageNumber;
        this.pageSize = pageSize;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}
