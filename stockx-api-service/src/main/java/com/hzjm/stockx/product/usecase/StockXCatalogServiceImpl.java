package com.hzjm.stockx.product.usecase;

import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.infrastructure.service.IApiRequestService;
import com.hzjm.stockx.data.StockXEndpoint;
import com.hzjm.stockx.data.StockXCurrencyCode;
import com.hzjm.stockx.infrastructure.StockXPageResponse;
import com.hzjm.stockx.product.data.dto.StockXProduct;
import com.hzjm.stockx.product.data.dto.StockXProductVariant;
import com.hzjm.stockx.product.data.dto.StockXProductVariantMarketData;
import com.hzjm.stockx.product.data.dto.StockXSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class StockXCatalogServiceImpl implements IStockXCatalogService {

    @Qualifier("apiRequestServiceWithAutoAuthImplMarketData")
    @Autowired
    IApiRequestService apiRequestService;

    /**
     * 获取一个产品的详细信息， GET 请求
     *
     * @param productId Unique identifier for a product
     * @return 注意此产品信息 中不包含 产品的变体信息
     */
    @Override
    public StockXProduct getSingleProduct(String productId) {
        HttpRequest<StockXProduct> httpRequest = StockXEndpoint.fetchSingleProduct(productId);
        return apiRequestService.doRequest(httpRequest);
    }

    /**
     * 获取一个产品的所有变体详细信息， GET 请求
     *
     * @param productId Unique identifier for a product
     * @return
     */
    @Override
    public List<StockXProductVariant> getAllProductVariants(String productId) {
        HttpRequest<List<StockXProductVariant>> httpRequest = StockXEndpoint.getAllVariantByProductId(productId);
        return apiRequestService.doRequest(httpRequest);
    }

    /**
     * 获取产品变体的市场数据，GET 请求
     *
     * @param productId    唯一产品ID
     * @param variantId    唯一变体ID
     * @param currencyCode 产品所在的地区
     * @return
     */
    @Override
    public StockXProductVariantMarketData getProductVariantMarketData(String productId, String variantId, StockXCurrencyCode currencyCode) {
        HttpRequest<StockXProductVariantMarketData> httpRequest = StockXEndpoint.getMarketDataBy(productId,variantId, currencyCode);
        return  apiRequestService.doRequest(httpRequest);
    }

    /**
     * 获取此 prduct 下的所有变体的 市场数据
     *
     * @param productId
     * @param currencyCode
     * @return
     */
    @Override
    public List<StockXProductVariantMarketData> getAllProductVariantMarketData(String productId, StockXCurrencyCode currencyCode) {
        HttpRequest<List<StockXProductVariantMarketData>> request = StockXEndpoint.getAllMarketDataBy(productId, currencyCode);
        return apiRequestService.doRequest(request);
    }

    /**
     * 根据关键词来搜索 StockX 的产品， GET 请求
     *
     * @param searchRequest 需要搜索的产品的参数的请求头
     * @return
     */
    @Override
    public StockXPageResponse<StockXProduct> searchProductsBy(StockXSearchRequest searchRequest) {
        Map<String, String> queryParameters = new HashMap<>();
        queryParameters.put("pageNumber", String.valueOf(searchRequest.getPageNumber()));
        queryParameters.put("pageSize", String.valueOf(searchRequest.getPageSize()));
        queryParameters.put("query", searchRequest.getQuery());
        HttpRequest<StockXPageResponse<StockXProduct>> httpRequest = StockXEndpoint.queryProductsInStockX(searchRequest.getQuery(), queryParameters);
        return apiRequestService.doRequest(httpRequest);
    }
}
