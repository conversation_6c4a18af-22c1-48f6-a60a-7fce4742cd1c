package com.hzjm.poizon.order.request;

import com.hzjm.poizon.order.response.OrderType;
import com.hzjm.poizon.order.response.PoizonOrderStatus;
import io.swagger.models.auth.In;
import lombok.Data;

@Data
public class GetPoizonOrderRequest {
    Integer page_no;
    Integer page_size = 100; // 100 per page is maximum
    String order_no;
    OrderType order_type;
    String express_no;
    PoizonOrderStatus order_status;

    //Start time of order creation. Format: yyyy-MM-dd HH:mm:ss. Note: The maximum interval between start and end time is 7 days. Default is 7 days before the current time
    String start_created;

    //End time of order creation. Format: yyyy-MM-dd HH:mm:ss. Note: The maximum interval between start and end time is 7 days. Default is the current time
    String end_created;

    Long sku_id;
    Long spu_id;
    Boolean warehouse_code;

    //Sort by order time. Default is false (ascending order)
    Boolean order_by_create_time_desc;
}
