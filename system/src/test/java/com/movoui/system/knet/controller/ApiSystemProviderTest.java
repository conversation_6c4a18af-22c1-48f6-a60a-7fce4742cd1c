package com.movoui.system.knet.controller;

import com.alibaba.fastjson.JSON;
import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.ListingStatus;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingQueryDto;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * ApiSystemProvider 测试类
 *
 * <AUTHOR>
 * @date 2025/2/26
 */
@SpringBootTest
@ActiveProfiles("test")
public class ApiSystemProviderTest {

    @Test
    public void testPlatformListingQueryDto() {
        // 测试请求DTO的序列化和反序列化
        PlatformListingQueryDto dto = new PlatformListingQueryDto();
        dto.setCurrent(1);
        dto.setPageSize(10);
        dto.setSaleStatus(ListingStatus.INACTIVE);
        dto.setPlatform(SourcePlatform.B2B_SHOP);
        dto.setAccount(ListingAccount.B2B_SHOP);

        String json = JSON.toJSONString(dto);
        System.out.println("请求DTO JSON: " + json);

        PlatformListingQueryDto parsedDto = JSON.parseObject(json, PlatformListingQueryDto.class);
        System.out.println("解析后的DTO: " + parsedDto);

        // 验证枚举值
        assert parsedDto.getSaleStatus() == ListingStatus.INACTIVE;
        assert parsedDto.getPlatform() == SourcePlatform.B2B_SHOP;
        assert parsedDto.getAccount() == ListingAccount.B2B_SHOP;
        assert parsedDto.getCurrent().equals(1);
        assert parsedDto.getPageSize().equals(10);
    }

    @Test
    public void testCreateSampleRequest() {
        // 创建示例请求，对应原始SQL查询
        PlatformListingQueryDto dto = new PlatformListingQueryDto();
        dto.setCurrent(1);
        dto.setPageSize(20);
        dto.setSaleStatus(ListingStatus.INACTIVE);
        dto.setPlatform(SourcePlatform.B2B_SHOP);
        dto.setAccount(ListingAccount.B2B_SHOP);

        System.out.println("示例请求JSON:");
        System.out.println(JSON.toJSONString(dto, true));
        
        System.out.println("\n对应的SQL查询条件:");
        System.out.println("sale_status = 'INACTIVE'");
        System.out.println("platform = 'B2B_SHOP'");
        System.out.println("account = 'B2B_SHOP'");
        System.out.println("ORDER BY id DESC");
        System.out.println("LIMIT " + ((dto.getCurrent() - 1) * dto.getPageSize()) + ", " + dto.getPageSize());
    }
}
