import com.hzjm.common.infrastructure.service.ApiRequestServiceImpl;
import com.hzjm.common.infrastructure.service.IApiRequestService;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.service.ISysProdService;
import com.hzjm.service.service.impl.SysProdServiceImpl;
import com.hzjm.stockx.infrastructure.ApiRequestServiceWithAutoAuthImpl;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.List;

//@SpringBootTest
//public class SystemServiceTests {
//
//
//    @Test
//    void contextLoads() {
//
//    }
//
//}
