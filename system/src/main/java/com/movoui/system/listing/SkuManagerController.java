package com.movoui.system.listing;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.product.data.dto.StockXMappingDto;
import com.hzjm.crosslisting.product.data.vo.KnetProductVo;
import com.hzjm.crosslisting.product.entity.KnetProduct;
import com.hzjm.crosslisting.product.repository.IKnetProductService;
import com.hzjm.crosslisting.product.repository.IProductUseCase;
import com.hzjm.crosslisting.product.usecase.IThirdPlatformProductInfoFetcher;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.service.ISysProdService;
import com.hzjm.stockx.product.data.dto.StockXProduct;
import com.hzjm.stockx.product.data.dto.StockXSearchRequest;
import com.movoui.system.knet.dto.respose.StockXProductResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "SysSku Map 映射至Cross listing 三方平台的工具")
@RestController
@RequestMapping("/sku_mapper")
public class SkuManagerController {

    @Autowired
    IThirdPlatformProductInfoFetcher skuMapService;

    @Autowired
    ISysProdService sysProdService;

    @GetMapping("/search")
    public HttpResult<List<KnetProductVo>> create(@RequestParam String sku,
                                                  @RequestParam String platform) {
        if (ObjectUtils.isEmpty(sku)) {
            return HttpResult.error(400, "Sku can't be empty.");
        }

        if (ObjectUtils.isEmpty(platform)) {
            return HttpResult.error(400, "Platform can't be empty.");
        }

        try {
            List<KnetProductVo> productVos = new ArrayList<>();
            List<KnetProduct> products = skuMapService.searchKnetProduct(sku, SourcePlatform.createFrom(platform));
            for (KnetProduct product: products) {
                KnetProductVo vo = new KnetProductVo();
                BeanUtils.copyProperties(product, vo);
                productVos.add(vo);

            }
            return HttpResult.ok(productVos);
        }catch (IllegalArgumentException | BaseException e) {
            return HttpResult.error(500, e.getMessage());
        }
    }

    @GetMapping("/search/market_data")
    public HttpResult<List<KnetProductVo>> searchMarketData(@RequestParam String sku,
                                                            @RequestParam String platform) {
        if (ObjectUtils.isEmpty(sku)) {
            return HttpResult.error(400, "Sku can't be empty.");
        }

        if (ObjectUtils.isEmpty(platform)) {
            return HttpResult.error(400, "Platform can't be empty.");
        }

        try {
            List<KnetProductVo> productVos = new ArrayList<>();
            List<KnetProduct> products = skuMapService.updateKnetProductMarketData(sku, SourcePlatform.createFrom(platform));
            for (KnetProduct product: products) {
                KnetProductVo vo = new KnetProductVo();
                BeanUtils.copyProperties(product, vo);
                productVos.add(vo);
            }
            return HttpResult.ok(productVos);
        }catch (IllegalArgumentException | BaseException e) {
            return HttpResult.error(500, e.getMessage());
        }
    }

    /**
     *  指定 Sku + Size + 平台 ，来去查询 对应的产品并创建至数据库
     * @param sku
     * @param size
     * @param platform
     * @return
     */
    @PutMapping("/create")
    public HttpResult<List<KnetProductVo>> create(@RequestParam String sku,
                                              @RequestParam String size,
                                              @RequestParam String platform) {
        if (ObjectUtils.isEmpty(sku)) {
            return HttpResult.error(400, "Sku can't be empty.");
        }

        if (ObjectUtils.isEmpty(size)) {
            return HttpResult.error(400, "Size can't be empty.");
        }

        if (ObjectUtils.isEmpty(platform)) {
            return HttpResult.error(400, "Platform can't be empty.");
        }

        try {
            List<KnetProductVo> productVos = new ArrayList<>();
            List<KnetProduct> products = skuMapService.createKnetProduct(sku, size, SourcePlatform.createFrom(platform));
            for (KnetProduct product: products) {
                KnetProductVo vo = new KnetProductVo();
                BeanUtils.copyProperties(product, vo);
                productVos.add(vo);

            }
            return HttpResult.ok(productVos);
        }catch (IllegalArgumentException | BaseException e) {
            return HttpResult.error(500, e.getMessage());
        }
    }

    /**
     *  根据SKU + Size + 平台 去查询已有的 KnetProduct 数据库，更新最新的市场数据
     * @param sku
     * @param size
     * @param platform
     * @return
     */
    @GetMapping("/get_market_data")
    public HttpResult<KnetProductVo> getMarketData(@RequestParam String sku,
                                            @RequestParam String size,
                                            @RequestParam String platform) {
        if (ObjectUtils.isEmpty(sku)) {
            return HttpResult.error(400, "Sku can't be empty.");
        }

        if (ObjectUtils.isEmpty(size)) {
            return HttpResult.error(400, "Size can't be empty.");
        }

        if (ObjectUtils.isEmpty(platform)) {
            return HttpResult.error(400, "Platform can't be empty.");
        }

        try {
            KnetProduct product = skuMapService.updateKnetProductMarketData(sku, size, SourcePlatform.createFrom(platform));
            KnetProductVo vo = new KnetProductVo();
            BeanUtils.copyProperties(product, vo);
            return HttpResult.ok(vo);
        }catch (IllegalArgumentException | BaseException e) {
            return HttpResult.error(500, e.getMessage());
        }
    }

    @GetMapping("/get_ware_prod_category")
    public HttpResult<List<String>> getAllWareInSysProdCategory() {
        List<SysProd> inWareSysProd = sysProdService.getDistinctSkuAndSpec();
        return  HttpResult.ok(inWareSysProd.stream().map(sysProd -> sysProd.getSku() + "_" + sysProd.getSpec()).collect(Collectors.toList()));
    }

    @GetMapping("/stockx_product")
    public HttpResult<StockXProductResponse> fetchStockProductBy(@RequestParam String productName) {
        if (ObjectUtils.isEmpty(productName)) {
            return HttpResult.error("invalid product name");
        }

        List<StockXProduct> products = skuMapService.searchProductBy(productName);
        StockXProductResponse response = new StockXProductResponse();
        response.setItems(products);

        return HttpResult.ok(response);
    }

    @PutMapping("/stockx_product_mapping")
    public HttpResult<List<KnetProduct>> stockxProductMapping(@RequestBody StockXMappingDto mappingInfo) {
        if (ObjectUtils.isEmpty(mappingInfo)) {
            return HttpResult.error("Invalid mapping Info");
        }

        if (ObjectUtils.isEmpty(mappingInfo.getSku()) || ObjectUtils.isEmpty(mappingInfo.getStockXProduct())) {
            return HttpResult.error("missing key info to map stock product.");
        }

        List<KnetProduct> createdProducts = skuMapService.mapKnetProductBy(mappingInfo);

        if (ObjectUtils.isEmpty(createdProducts)) {
            return HttpResult.error(500, "mapping到了 空的数据，请尝试修改 '忽略尺码/性别' 设置, 再次印射 stockX 数据");
        }else {
            return HttpResult.ok(createdProducts);
        }

    }
}
