package com.movoui.system.knet.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.crosslisting.listing.data.dto.PlatformListingQueryDto;
import com.hzjm.crosslisting.listing.data.vo.PlatformListingQueryVo;
import com.hzjm.crosslisting.listing.repository.IKnetProductListingService;
import com.hzjm.crosslisting.listing.repository.IPlatformListingService;
import com.hzjm.crosslisting.product.data.dto.FetchProductMarketDataDto;
import com.hzjm.crosslisting.product.data.vo.B2bKnetInventoryDataVo;
import com.hzjm.crosslisting.product.data.vo.KnetProductMarketDataVo;
import com.hzjm.crosslisting.product.usecase.IKnetProductMarketDataService;
import com.hzjm.service.model.VO.HotSkuRankVo;
import com.hzjm.service.service.DashboardService;
import com.movoui.system.knet.dto.request.KnetGroupGetInventoryReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/26 17:58
 * @description: knet对外提供服务接口
 */
@Slf4j
@RestController
@RequestMapping("/api/system")
public class ApiSystemProvider {
    @Resource
    private IKnetProductMarketDataService marketDataService;
    @Resource
    private DashboardService dashboardService;
    @Resource
    private IKnetProductListingService knetProductListingService;

    /**
     * 对外提供服务接口
     * 根据 Sku + 一组size 来查询所有的平台的市场数据，包含 最低价，闪电最低价，以及需要的计费明细
     * 此接口使用数据库缓存查询
     *
     * @param dataDto
     * @return
     */
    @PostMapping("/get_prod_market_data")
    public HttpResult<List<KnetProductMarketDataVo>> getProductMarketData(@RequestBody FetchProductMarketDataDto dataDto) {
        try {
            dataDto.setSkipCache(false);
            List<KnetProductMarketDataVo> vo = marketDataService.fetchCachedProductMarketData(dataDto, 1);
            return HttpResult.ok(vo);
        } catch (BaseException e) {
            return HttpResult.error(e.getCode(), e.getMessage());
        }
    }

    /**
     * 获取 kg 热门 sku 排行
     *
     * @return kg 热门 sku 排行
     */
    @GetMapping("/get_hot_sku")
    public HttpResult<List<HotSkuRankVo>> queryProductTop20ByCache() {
        try {
            List<HotSkuRankVo> skuRanks = dashboardService.queryProductTop20ByCache(new TableDataSearchDto());
            return HttpResult.ok(skuRanks);
        } catch (Exception e) {
            return HttpResult.error(e.getMessage());
        }
    }

    /**
     * 获取库存数据
     *
     * @param req sku
     * @return 库存数据
     */
    @PostMapping("/get_inventory_data")
    public HttpResult<List<B2bKnetInventoryDataVo>> getInventoryData(@RequestBody KnetGroupGetInventoryReq req) {
        try {
            List<B2bKnetInventoryDataVo> inventoryDataBySku = knetProductListingService.getInventoryDataBySku(req.getSkus());
            return HttpResult.ok(inventoryDataBySku);
        } catch (BaseException e) {
            return HttpResult.error(e.getCode(), e.getMessage());
        }
    }
}
