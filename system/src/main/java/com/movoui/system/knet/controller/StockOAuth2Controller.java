package com.movoui.system.knet.controller;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.stockx.infrastructure.StockxApiTokenGuardian;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@RestController
public class StockOAuth2Controller {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private StockxApiTokenGuardian apiTokenGuardian;

    private final String STOCKX_STATE_KEY = "oauth2:stockx_state_key";

    @SuppressWarnings("null")
    @ApiOperation("StockX OAuth2 Login")
    @GetMapping("/oauth2/stockx/login")
    public HttpResult<String> stockxLogin(HttpServletResponse response, @RequestParam String email) {
        if (email == null) {
            return HttpResult.error("Email is required!");
        }

        email = email.toLowerCase().trim();

        String stockxLoginUrl = apiTokenGuardian.getStockXLoginURL(email);
        if (stockxLoginUrl == null) {
            return HttpResult.error("This email was not found! email: " + email);
        }

        redisTemplate.opsForValue().set(STOCKX_STATE_KEY, email, 60, TimeUnit.SECONDS);

        log.info("StockOAuth2Controller:stockxLogin(): StockX OAuth2 login URL: {}", stockxLoginUrl);

        try {
            response.sendRedirect(stockxLoginUrl);
        } catch (IOException e) {
            throw new BaseException("System error!");
        }

        return HttpResult.ok("Redirected!");
    }

    @ApiOperation("StockX OAuth2 callback")
    @GetMapping("/oauth2/stockx/callback")
    public HttpResult<String> stockxAuthorized(@RequestParam String code, @RequestParam String state) {
        String stateValue = redisTemplate.opsForValue().get(STOCKX_STATE_KEY);
        if (!state.equals(stateValue)) {
            log.warn("StockOAuth2Controller:stockxAuthorized(): Received invalid callback! state: {}, expected: {}", state, stateValue);
            return HttpResult.error(403, "Invalid callback!");
        }
        redisTemplate.delete(STOCKX_STATE_KEY);

        log.info("StockOAuth2Controller:stockxAuthorized(): Stock X authCode is:" + code);

        return HttpResult.ok(code);
    }
}
