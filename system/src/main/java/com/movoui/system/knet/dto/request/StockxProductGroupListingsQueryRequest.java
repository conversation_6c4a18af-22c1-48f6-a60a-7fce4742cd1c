package com.movoui.system.knet.dto.request;

import com.movoui.system.common.Pageable;
import com.movoui.system.knet.enums.UserPlatform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/23 14:18
 * @description: stockx sku 分组查询条件
 */
@Data
public class StockxProductGroupListingsQueryRequest implements Pageable {
    @ApiModelProperty(value = "页码数")
    private Integer pageNo;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;

    @ApiModelProperty(value = " supply(商家端口)  admin（管理端）  warehouse（仓库端）")
    private UserPlatform userPlatform;

    @ApiModelProperty(value = "sku")
    private String sku;
}
