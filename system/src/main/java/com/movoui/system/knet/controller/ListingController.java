package com.movoui.system.knet.controller;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.crosslisting.enums.ProductType;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.listing.data.vo.ListingPricingHistoryVo;
import com.hzjm.crosslisting.listing.entity.KnetProductListing;
import com.hzjm.crosslisting.listing.entity.ListingPricingHistory;
import com.hzjm.crosslisting.listing.repository.IKnetProductListingService;
import com.hzjm.crosslisting.listing.repository.IListingPricingHistoryService;
import com.hzjm.crosslisting.listing.usecase.IKnetListingCalibrateService;
import com.hzjm.crosslisting.pricing.data.dto.RevenueCalculateItem;
import com.hzjm.crosslisting.pricing.entity.RevenueDetail;
import com.hzjm.crosslisting.pricing.usecase.revenue.PricingDetailStrategyContext;
import com.hzjm.crosslisting.product.data.dto.FetchDefectProductMarketDataDto;
import com.hzjm.crosslisting.product.data.dto.FetchProductMarketDataDto;
import com.hzjm.crosslisting.product.data.vo.KnetProductMarketDataVo;
import com.hzjm.crosslisting.product.entity.KnetProduct;
import com.hzjm.crosslisting.product.repository.IKnetProductService;
import com.hzjm.crosslisting.product.usecase.IKnetProductMarketDataService;
import com.hzjm.crosslisting.product.usecase.IThirdPlatformProductInfoFetcher;
import com.hzjm.crosslisting.utils.StringProcessedUtils;
import com.movoui.system.knet.dto.request.BatchRevenueDetailReq;
import com.movoui.system.knet.dto.respose.BatchRevenueDetailResponse;
import com.movoui.system.knet.dto.respose.PricingModifyHistoryResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "Cross Listing")
@Slf4j
@RestController
@RequestMapping("/cross_listing")
public class ListingController {

    @Autowired
    IThirdPlatformProductInfoFetcher skuMapService;

    @Autowired
    IKnetProductMarketDataService marketDataService;

    @Autowired
    IKnetProductService knetProductService;

    @Autowired
    PricingDetailStrategyContext revenueCalculator;

    @Autowired
    IKnetProductListingService knetProductListingService;

    @Autowired
    IListingPricingHistoryService listingPricingHistoryService;

    @Autowired
    IKnetListingCalibrateService listingCalibrateService;

    /**
     * 根据 Sku + 一组size 来查询所有的平台的市场数据，包含 最低价，闪电最低价，以及需要的计费明细
     * 此接口使用数据库缓存查询
     * @param dataDto
     * @return
     */
    @PostMapping("get_prod_market_data")
    @TrimParam
    public HttpResult<List<KnetProductMarketDataVo>> getProductMarketData(@RequestBody FetchProductMarketDataDto dataDto) {
        Integer userId = JwtContentHolder.getUserId();
        try {
            List<KnetProductMarketDataVo> vo = marketDataService.fetchCachedProductMarketData(dataDto, userId);
            return HttpResult.ok(vo);
        }catch (BaseException e) {
            return HttpResult.error(e.getCode(), e.getMessage());
        }
    }

    @Deprecated
    @PostMapping("get_prod_market_data/stockx")
    public HttpResult<List<KnetProductMarketDataVo>> getProductMarketDataForStockX(@RequestBody FetchProductMarketDataDto dataDto) {
        Integer userId = JwtContentHolder.getUserId();
        try {
            List<KnetProductMarketDataVo> vo = marketDataService.fetchCachedProductMarketDataForStockX(dataDto, userId);
            return HttpResult.ok(vo);
        }catch (BaseException e) {
            return HttpResult.error(e.getCode(), e.getMessage());
        }
    }

    @GetMapping("/product_type")
    public HttpResult<ProductType> checkProductType(@RequestParam String sku, String size) {
        if (ObjectUtils.isEmpty(sku)) {
            return HttpResult.error("Invalid sku to check product category");
        }

        // 先从数据库获取, 获取到同一SKU的所有数据列表
        KnetProduct knetProduct = knetProductService.getOne(Wrappers.<KnetProduct>lambdaQuery()
                .eq(KnetProduct::getSku, StringProcessedUtils.processSkuFormat(sku))
                .last("LIMIT 1")
        );

        // 有匹配的结果直接返回
        if (ObjectUtils.isNotEmpty(knetProduct)) {
            return HttpResult.ok(knetProduct.getProductType());
        }

        // 没有匹配的结果我们直接去 三方印射 一个 再返回这里以
        List<KnetProduct> newCreatedKnetProductGoatIs = skuMapService.createKnetProduct(
                sku,
                size,
                SourcePlatform.GOAT_INSTANT_SHIP
        );

        // 搜索到匹配结果 直接返回
        if (ObjectUtils.isNotEmpty(newCreatedKnetProductGoatIs)) {
            return HttpResult.ok(newCreatedKnetProductGoatIs.get(0).getProductType());
        }

        throw new BaseException("Can't match any knet product to listing, please contact support create this unknown sku.");
    }

    /**
     * 根据 Sku + 一组size 来查询所有的平台的市场数据，包含 最低价，闪电最低价，以及需要的计费明细
     * 此接口使用数据库缓存查询
     * @param dataDto
     * @return
     */
    @Deprecated
    @PostMapping("instant/get_prod_market_data/stockx")
    public HttpResult<List<KnetProductMarketDataVo>> getProductMarketDataFromStockX(@RequestBody FetchProductMarketDataDto dataDto) {
        Integer userId = JwtContentHolder.getUserId();

        // 提交的数据为空， 直接抛出错误
        if (dataDto == null || dataDto.getSizeRange().isEmpty() || dataDto.getSku().isEmpty()) {
            String errorInfo = "Submit empty data when query product market data.";
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        // 先从数据库获取, 获取到同一SKU的所有数据列表
        List<KnetProduct> allProductList = knetProductService.list(Wrappers.<KnetProduct>lambdaQuery()
                .eq(KnetProduct::getPlatform, SourcePlatform.STOCK_X)
                .eq(KnetProduct::getSku, StringProcessedUtils.processSkuFormat(dataDto.getSku()))
                .in(KnetProduct::getSize, dataDto.getSizeRange())
        );

        if (allProductList
                .stream()
                .noneMatch(product -> SourcePlatform.STOCK_X.equals(product.getPlatform()))) {

            // 新创建的 StockX 的 KnetProduct
            List<KnetProduct> newCreatedKnetProductStockX = skuMapService.createKnetProduct(
                    dataDto.getSku(),
                    dataDto.getSizeRange().get(0),
                    SourcePlatform.STOCK_X
            );

            allProductList.addAll(newCreatedKnetProductStockX);
        }

        // 重新在三方平台创建数据也无法找到上架所需的数据，则抛出错误
        if (allProductList.isEmpty()) {
            String errorInfo = "Can't find KnetProduct record by sku & sizeRange when query product Market from dataBase & stockX. sku: "
                    + dataDto.getSku()
                    +  ", sizeRange: "
                    + dataDto.getSizeRange().toString();
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        // 按 size 分组
        Map<String, KnetProduct> knetProductMap = allProductList
                .stream()
                .collect(Collectors.toMap(KnetProduct::getSize, Function.identity(), (existing, replacement) -> replacement));

        List<KnetProductMarketDataVo> vos = new ArrayList<>();
        knetProductMap
                .values()
                .parallelStream()
                .forEach(singleKnetProduct -> {
            try {
                vos.add(marketDataService.fetchRemoteMarketDataSpecifyPlatform(dataDto.getSku(), singleKnetProduct.getSize(), singleKnetProduct, singleKnetProduct.getPlatform(), userId));
            }catch (Exception e) {
                log.error("fetch stockX remote market data error with sku: " + dataDto.getSku() + ", size: " + singleKnetProduct.getSize() + ", message: " + e);
            }
        });

        return HttpResult.ok(vos);
    }

    /**
     * 根据 Sku + 一组 size 来查询 所有平台的市场数据， 并返回市场最低价 以及计费明细
     * 此接口直接请求数据库缓存
     * @param dataDto
     * @return
     */
    @PostMapping("/instant/get_prod_market_data")
    public HttpResult<List<KnetProductMarketDataVo>> getProductMarketDataInstant(@RequestBody FetchProductMarketDataDto dataDto) {
        Integer userId = JwtContentHolder.getUserId();

        // 提交的数据为空， 直接抛出错误
        if (dataDto == null || dataDto.getSizeRange().isEmpty() || dataDto.getSku().isEmpty()) {
            String errorInfo = "Submit empty data when query product market data.";
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        // 获取 是否排除 stockX
        boolean excludedStockX = ObjectUtils.isNotEmpty(dataDto.isExcludedStockX()) && dataDto.isExcludedStockX();

        // 先从数据库获取, 获取到同一SKU的所有数据列表
        List<KnetProduct> allProductList = knetProductService.list(Wrappers.<KnetProduct>lambdaQuery()
                .eq(KnetProduct::getSku, StringProcessedUtils.processSkuFormat(dataDto.getSku()))
                .ne(excludedStockX, KnetProduct::getPlatform, SourcePlatform.STOCK_X)
                .in(KnetProduct::getSize, dataDto.getSizeRange())
        );

        // 仅当不排除 stockX 平台时才 加入对stockX 平台的 判断
        if (!excludedStockX) {
            if (allProductList
                    .stream()
                    .noneMatch(product -> SourcePlatform.STOCK_X.equals(product.getPlatform()))) {

                // 新创建的 StockX 的 KnetProduct
                List<KnetProduct> newCreatedKnetProductStockX = skuMapService.createKnetProduct(
                        dataDto.getSku(),
                        dataDto.getSizeRange().get(0),
                        SourcePlatform.STOCK_X
                );

                allProductList.addAll(newCreatedKnetProductStockX);
            }
        }

        // 从数据库取不到 Goat 的 Knet Product 数据,则去三方创建，因为 Ebay 实际上与 Goat 关联，就一起连 Ebay 也创建
        if (allProductList
                .stream()
                .noneMatch(product -> SourcePlatform.GOAT_INSTANT_SHIP.equals(product.getPlatform()))) {

            // 新创建的 Goat Knet Product
            List<KnetProduct> newCreatedKnetProductGoatIs = skuMapService.createKnetProduct(
                    dataDto.getSku(),
                    dataDto.getSizeRange().get(0),
                    SourcePlatform.GOAT_INSTANT_SHIP
            );
            allProductList.addAll(newCreatedKnetProductGoatIs);

            // 新创建的 Ebay Knet Product
            List<KnetProduct> newCreatedKnetProductEbay = skuMapService.createKnetProduct(
                    dataDto.getSku(),
                    dataDto.getSizeRange().get(0),
                    SourcePlatform.EBAY
            );
            allProductList.addAll(newCreatedKnetProductEbay);
        }

        // 从数据库 取不到 Ebay 的 Knet Product 数据
        if (allProductList.stream().noneMatch(product -> SourcePlatform.EBAY.equals(product.getPlatform()))) {
            List<KnetProduct> newCreatedKcProduct  = skuMapService.createKnetProduct(
                    dataDto.getSku(),
                    dataDto.getSizeRange().get(0),
                    SourcePlatform.EBAY
            );
            allProductList.addAll(newCreatedKcProduct);
        }

        // 从数据库 取不到 Poizon 的 Knet Product 的数据
//        if (allProductList.stream().noneMatch(product -> SourcePlatform.POIZON.equals(product.getPlatform()))) {
//            List<KnetProduct> newCreatedKcProduct  = skuMapService.createKnetProduct(
//                    dataDto.getSku(),
//                    dataDto.getSizeRange().get(0),
//                    SourcePlatform.POIZON
//            );
//            allProductList.addAll(newCreatedKcProduct);
//        }

        // 从数据库 取不到 Kicks Crew 的 Knet Product 数据
        if (allProductList.stream().noneMatch(product -> SourcePlatform.KICKS_CREW.equals(product.getPlatform()))) {
            List<KnetProduct> newCreatedKcProduct  = skuMapService.createKnetProduct(
                    dataDto.getSku(),
                    dataDto.getSizeRange().get(0),
                    SourcePlatform.KICKS_CREW
            );
            allProductList.addAll(newCreatedKcProduct);
        }

        // 过滤出提交的的 sizes
        allProductList = allProductList
                .stream()
                .filter(product -> dataDto.getSizeRange().contains(product.getSize()))
                .collect(Collectors.toList());

        // 重新在三方平台创建数据也无法找到上架所需的数据，则抛出错误
        if (allProductList.isEmpty()) {
            String errorInfo = "Can't find KnetProduct record by sku & sizeRange when query product Market from dataBase & third platform. sku: "
                    + dataDto.getSku()
                    +  ", sizeRange: "
                    + dataDto.getSizeRange().toString();
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        // 按组区分
        Map<String, List<KnetProduct>> knetProductGroupBySize = allProductList
                .stream()
                .collect(Collectors.groupingBy(KnetProduct::getSize));


        List<KnetProductMarketDataVo> marketDataVos = new ArrayList<>();
        for (String size: dataDto.getSizeRange()) {
            // 在这里 对 size 进行 统一的 小写 匹配， 因为 数据库中 的 衣服尺码统一为小写
            try {
                marketDataVos.add(marketDataService.fetchRemoteMarketData(
                        dataDto.getSku(),
                        size.toLowerCase(),
                        knetProductGroupBySize.get(size),
                        userId,
                        excludedStockX)
                );
            }catch (BaseException e) {
                log.error(e.getMessage());
            }
        }

        return HttpResult.ok(marketDataVos);
    }

    @PostMapping("/instant/defect_market_data")
    public HttpResult<List<KnetProductMarketDataVo>> getDefectProductMarketDataInstant(@RequestBody FetchDefectProductMarketDataDto dataDto) {
        Integer userId = JwtContentHolder.getUserId();

        // 提交的数据为空， 直接抛出错误
        if (ObjectUtils.isEmpty(dataDto) && dataDto.getItems().isEmpty()) {
            String errorInfo = "Submit empty data when query product market data.";
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        return HttpResult.ok(marketDataService.fetchDefectMarketData(dataDto.getItems(), userId));
    }

    /**
     *  给定一个价格 和 平台计算 到手价明细
     * @param item 信息
     * @return
     */
    @PostMapping("revenue_detail")
    public HttpResult<RevenueDetail> getRevenueDetail(@RequestBody RevenueCalculateItem item) {
        try {
            RevenueDetail revenueDetail = revenueCalculator.calculateRevenueDetail(item.getAccount(),
                    item.getSalePrice(),
                    JwtContentHolder.getUserId()
            );
            return HttpResult.ok(revenueDetail);
        }catch (BaseException e) {
            return HttpResult.error(e.getCode(), e.getMessage());
        }
    }

    @PostMapping("batch_revenue_detail")
    public HttpResult<BatchRevenueDetailResponse> getRevenueDetailBatch(@RequestBody BatchRevenueDetailReq req) {
        if (ObjectUtils.isEmpty(req.getItems())) {
            return HttpResult.error(400, "submit empty list to calculate revenue.");
        }

        Integer userId =  JwtContentHolder.getUserId();

        if (ObjectUtils.isEmpty(userId)) {
            return HttpResult.error(401, "invalid token");
        }

        BatchRevenueDetailResponse response = new BatchRevenueDetailResponse();

        // 使用线程安全的集合
        List<BatchRevenueDetailResponse.RevenueDetailResponse> items = Collections.synchronizedList(new ArrayList<>());

        req.getItems().parallelStream().forEach(revenueCalculateItem -> {
            BatchRevenueDetailResponse.RevenueDetailResponse singleResponse = new BatchRevenueDetailResponse.RevenueDetailResponse();
            singleResponse.setOneId(revenueCalculateItem.getOneId());

            // 使用线程安全的集合
            List<RevenueDetail> revenueDetails = Collections.synchronizedList(new ArrayList<>());

            revenueCalculateItem.getCalculateItems().parallelStream().forEach(singlePlatformItem -> {
                try {
                    RevenueDetail revenueDetail = revenueCalculator.calculateRevenueDetail(singlePlatformItem.getAccount(),
                            singlePlatformItem.getSalePrice(),
                            userId
                    );
                    revenueDetails.add(revenueDetail);
                } catch (Exception e) {
                    log.error("calculate revenue detail one id:" + revenueCalculateItem.getOneId()
                            + ", account: " + singlePlatformItem.getAccount()
                            + ", price:" + singlePlatformItem.getSalePrice(), e);
                    RevenueDetail failed = RevenueDetail.ZERO();
                    failed.setPlatform(singlePlatformItem.getAccount().toSourcePlatform());
                    revenueDetails.add(failed);
                }
            });

            singleResponse.setRevenueDetails(revenueDetails);
            items.add(singleResponse);
        });

        response.setItems(items);
        return HttpResult.ok(response);
    }

    @GetMapping("/pricing_history")
    public HttpResult<PricingModifyHistoryResponse> getPricingHistory(@RequestParam String knetListingId) {
        //
        KnetProductListing knetProductListing = knetProductListingService
                .getOne(Wrappers.<KnetProductListing>lambdaQuery()
                        .eq(KnetProductListing::getKnetListingId, knetListingId)
                        .last("LIMIT 1")
                );

        if (knetProductListing == null) {
            return HttpResult.error(400, "Illegal Knet Listing Id.");
        }

        PricingModifyHistoryResponse response = new PricingModifyHistoryResponse();

        // 设置 StockX
        if (!ObjectUtils.isEmpty(knetProductListing.getStockxListingId())) {
            List<ListingPricingHistory> pricingHistories = listingPricingHistoryService.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(knetProductListing.getStockxListingId()),
                            ListingPricingHistory::getListingId,
                            knetProductListing.getStockxListingId())
            );

            if (!pricingHistories.isEmpty()) {
                response.setStockX(pricingHistories
                        .stream()
                        .map(ListingPricingHistoryVo::createFrom)
                        .sorted(Comparator.comparing(ListingPricingHistoryVo::getGmtCreate))
                        .collect(Collectors.toList()));
            }
        }

        // 设置 Goat
        if (!ObjectUtils.isEmpty(knetProductListing.getGoatListingId())) {
            List<ListingPricingHistory> pricingHistories = listingPricingHistoryService.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(knetProductListing.getGoatListingId()), ListingPricingHistory::getListingId, knetProductListing.getGoatListingId())
            );

            if (!pricingHistories.isEmpty()) {
                response.setGoat(pricingHistories
                        .stream()
                        .map(ListingPricingHistoryVo::createFrom)
                        .sorted(Comparator.comparing(ListingPricingHistoryVo::getGmtCreate))
                        .collect(Collectors.toList()));
            }
        }

        // 设置 GoatIs
        if (!ObjectUtils.isEmpty(knetProductListing.getGoatisListingId())) {
            List<ListingPricingHistory> pricingHistories = listingPricingHistoryService.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(knetProductListing.getGoatisListingId()), ListingPricingHistory::getListingId, knetProductListing.getGoatisListingId())
            );

            if (!pricingHistories.isEmpty()) {
                response.setGoatIs(pricingHistories
                        .stream()
                        .map(ListingPricingHistoryVo::createFrom)
                        .sorted(Comparator.comparing(ListingPricingHistoryVo::getGmtCreate))
                        .collect(Collectors.toList()));
            }
        }

        // 设置 Kc
        if (ObjectUtils.isNotEmpty(knetProductListing.getKcListingId())) {
            List<ListingPricingHistory>  pricingHistories = listingPricingHistoryService.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(knetProductListing.getKcListingId()), ListingPricingHistory::getListingId, knetProductListing.getKcListingId())
            );

            if (!pricingHistories.isEmpty()) {
                response.setKc(pricingHistories
                        .stream()
                        .map(ListingPricingHistoryVo::createFrom)
                        .sorted(Comparator.comparing(ListingPricingHistoryVo::getGmtCreate))
                        .collect(Collectors.toList()));
            }
        }

        // 设置 Ebay
        if (ObjectUtils.isNotEmpty(knetProductListing.getEbayListingId())) {
            List<ListingPricingHistory>  pricingHistories = listingPricingHistoryService.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(knetProductListing.getEbayListingId()), ListingPricingHistory::getListingId, knetProductListing.getEbayListingId())
            );

            if (!pricingHistories.isEmpty()) {
                response.setEbay(pricingHistories
                        .stream()
                        .map(ListingPricingHistoryVo::createFrom)
                        .sorted(Comparator.comparing(ListingPricingHistoryVo::getGmtCreate))
                        .collect(Collectors.toList()));
            }
        }

        // 设置 Poizon
        if (ObjectUtils.isNotEmpty(knetProductListing.getPoizonListingId())) {
            List<ListingPricingHistory>  pricingHistories = listingPricingHistoryService.list(Wrappers.<ListingPricingHistory>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(knetProductListing.getPoizonListingId()), ListingPricingHistory::getListingId, knetProductListing.getPoizonListingId())
            );

            if (!pricingHistories.isEmpty()) {
                response.setPoizon(pricingHistories
                        .stream()
                        .map(ListingPricingHistoryVo::createFrom)
                        .sorted(Comparator.comparing(ListingPricingHistoryVo::getGmtCreate))
                        .collect(Collectors.toList()));
            }
        }

        return HttpResult.ok(response);
    }


    @GetMapping("knet_listing_calibration")
    public HttpResult<List<String>> calibrateNotTrulyActiveKGListing() {
        try {
            return HttpResult.ok(listingCalibrateService.releaseAllPlatformNotActiveKnetProductListing());
        }catch (Exception e) {
            return HttpResult.error(e.getMessage());
        }
    }

}
