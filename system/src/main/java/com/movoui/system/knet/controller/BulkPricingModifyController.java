package com.movoui.system.knet.controller;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.crosslisting.pricing.data.dto.BulkModifyPricingDto;
import com.hzjm.crosslisting.pricing.data.vo.BulkModifyPricingVo;
import com.hzjm.crosslisting.pricing.usecase.bulkmodify.BulkPricingModifyStrategyContext;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Api(tags = "批量改价计算工具")
@Slf4j
@RestController
@RequestMapping("/bulk_pricing_modify")
public class BulkPricingModifyController {

    @Autowired
    BulkPricingModifyStrategyContext pricingModifyStrategyContext;

    @PostMapping("tool")
    public HttpResult<BulkModifyPricingVo> bulkPricingModification(@RequestBody BulkModifyPricingDto bulkModifyPricingDto) {
        Integer userId = JwtContentHolder.getUserId();
        try {
            BulkModifyPricingVo modifyPricingResponse = new BulkModifyPricingVo();
            List<BulkModifyPricingVo.BulkModifyPricingItem> modifyPricingItems = new ArrayList<>();
            if (bulkModifyPricingDto.getItems().isEmpty()) {
                modifyPricingResponse.setItems(modifyPricingItems);
                return  HttpResult.ok(modifyPricingResponse);
            }

            bulkModifyPricingDto.getItems().forEach( modifyPricingItem -> {
                BulkModifyPricingVo.BulkModifyPricingItem resItem  = pricingModifyStrategyContext.calculateSalePrice(
                        modifyPricingItem, userId);
                resItem.setSku(modifyPricingItem.getSku());
                resItem.setSize(modifyPricingItem.getSize());
                resItem.setCost(modifyPricingItem.getPricingInfo().getCost().getAmount());
                modifyPricingItems.add(resItem);
            });

            modifyPricingResponse.setItems(modifyPricingItems);
            return HttpResult.ok(modifyPricingResponse);
        }catch (BaseException e) {
            return HttpResult.error(e.getCode(), e.getMessage());
        }
    }

}
