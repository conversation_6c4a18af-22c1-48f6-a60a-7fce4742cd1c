package com.movoui.system.knet.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum UserPlatform {
    SUPPLY("supply"),
    ADMIN("admin"),
    WAREHOUSE("warehouse");

    private final String userPlatform;

    UserPlatform(String userPlatform) {
        this.userPlatform = userPlatform;
    }

    @JsonValue
    @Override
    public String toString() {
        return userPlatform;
    }

    public boolean isAdmin() {
        return this == UserPlatform.ADMIN;
    }

    public boolean isSupply() {
        return this == UserPlatform.SUPPLY;
    }

    public boolean isWarehouse() {
        return this == UserPlatform.WAREHOUSE;
    }

    public static UserPlatform fromString(String value) {
        for (UserPlatform platform : UserPlatform.values()) {
            if (platform.userPlatform.equalsIgnoreCase(value)) {
                return platform;
            }
        }
        throw new IllegalArgumentException("Unknown user platform: " + value);
    }
}
