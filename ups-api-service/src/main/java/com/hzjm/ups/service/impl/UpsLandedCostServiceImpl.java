package com.hzjm.ups.service.impl;

import com.hzjm.ups.config.UpsConfig;
import com.hzjm.ups.model.ErrorResponse;
import com.hzjm.ups.model.auth.response.OAuthAccount;
import com.hzjm.ups.model.landedcost.request.LandedCostRequest;
import com.hzjm.ups.model.landedcost.response.LandedCostResponse;
import com.hzjm.ups.service.ApiService;
import com.hzjm.ups.utils.ResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.UUID;

/**
 * UPS到岸成本报价服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UpsLandedCostServiceImpl {

    @Resource
    private ApiService apiService;
    private final UpsOAuthServiceImpl upsOAuthService;
    private final UpsConfig upsConfig;


    public LandedCostResponse calculateLandedCost(LandedCostRequest detail,String accountNumber) {
        try {
            log.info("Landed cost calculation request - detail: {}", detail);
            // 初始化账号信息
            OAuthAccount oAuthAccount = new OAuthAccount(accountNumber,upsConfig);

            // 获取访问令牌
            String accessToken = "Bearer " + upsOAuthService.getAccessToken(accountNumber);
            String transId = UUID.randomUUID().toString();

            detail.setTransID(transId)
                  .setAlversion("1")
                  .setAllowPartialLandedCostResult(false);

            // 发出请求的客户端/源应用程序的标识符。长度 512
            String transactionSrc = "knet";
            Response<LandedCostResponse> response = apiService.calculateLandedCost(
                    accessToken,
                    transId,
                    transactionSrc,
                    oAuthAccount.getAccountNumber(),
                    detail
            ).execute();

            if (response.isSuccessful()) {
                LandedCostResponse landedCostResponse = response.body();
                log.info("Successfully calculated landed cost");
                return landedCostResponse;
            } else {
                ErrorResponse errorResponse = ResponseUtils.parseErrorResponse(response);
                log.error("Failed to calculate landed cost. Code: {}, Error: {}",
                        errorResponse.getCode(), errorResponse.getMsg());
                throw new IllegalStateException(String.format("Failed to calculate landed cost. Code: %s, Error: %s",
                        errorResponse.getCode(), errorResponse.getMsg()));
            }
        } catch (IOException e) {
            log.error("Error calling UPS landed cost API", e);
            throw new IllegalStateException("Error calling UPS landed cost API", e);
        }
    }
}
