package com.hzjm.ups.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * 日志记录到 sys_ups_log
 * db: ddl.sql
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LoggingInterceptor implements Interceptor {
    private final JdbcTemplate jdbcTemplate;

    private static final ThreadLocal<Integer> currentUserId = new ThreadLocal<>();

    @NotNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        String requestBody = null;

        if (request.body() != null) {
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            requestBody = buffer.readString(StandardCharsets.UTF_8);
        }

        Response response = chain.proceed(request);

        // 读取响应体
        String responseBodyString = null;
        ResponseBody responseBody = response.body();
        if (responseBody != null) {
            BufferedSource source = responseBody.source();
            source.request(Long.MAX_VALUE);
            Buffer buffer = source.getBuffer();
            responseBodyString = buffer.clone().readString(StandardCharsets.UTF_8);

            // 保存日志到数据库
            try {
                String sql = "INSERT INTO sys_ups_log (url, response_code,request_account, request, response, gmt_create, create_by_id) VALUES (?, ?, ?, ?, ?, ?, ?)";
                jdbcTemplate.update(sql,
                        request.url().toString(),
                        String.valueOf(response.code()),
                        request.header("accountNumber"),
                        requestBody,
                        responseBodyString,
                        LocalDateTime.now(),
                        currentUserId.get()
                );
            } catch (Exception e) {
                log.error("Failed to save UPS log", e);
            }

            return response.newBuilder()
                    .body(ResponseBody.create(responseBody.contentType(), responseBodyString))
                    .build();
        }

        return response;
    }

    public static void setUserId(Integer userId) {
        if (userId != null) {
            currentUserId.set(userId);
        }
    }

    public static void clearUserId() {
        currentUserId.remove();
    }

}
