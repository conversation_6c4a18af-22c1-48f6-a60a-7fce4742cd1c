package com.hzjm.ups.model.rate.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PackageWeight {
    @JsonProperty("UnitOfMeasurement")
    @ApiModelProperty(value = "单位",required = true)
    private UnitOfMeasurement unitOfMeasurement;

    @JsonProperty("Weight")
    @ApiModelProperty(value = "重量",required = true)
    private String weight;
}
