package com.hzjm.ups.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ShipmentChargeType {
    TRANSPORTATION("01", "Transportation"),
    DUTIES_AND_TAXES("02", "Duties and Taxes"),
    BROKER_OF_CHOICE("03", "Broker of Choice"),
    BROKER_OF_CHOICE_TRANSPORTATION("04", "Broker of Choice and Transportation"),
    DUTIES_TAXES_AND_TRANSPORTATION("05", "Duties, Taxes and Transportation");

    private final String code;
    private final String description;

    @JsonValue
    public String getCode() {
        return code;
    }

    public static ShipmentChargeType fromCode(String code) {
        for (ShipmentChargeType chargeType : values()) {
            if (chargeType.getCode().equals(code)) {
                return chargeType;
            }
        }
        throw new IllegalArgumentException("Invalid shipment charge type code: " + code);
    }
}
