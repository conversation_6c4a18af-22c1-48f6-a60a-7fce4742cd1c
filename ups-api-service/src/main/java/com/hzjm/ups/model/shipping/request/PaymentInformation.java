package com.hzjm.ups.model.shipping.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PaymentInformation {
    @ApiModelProperty("运费信息")
    @JsonProperty("ShipmentCharge")
    private List<ShipmentCharge> shipmentCharge;

    @JsonProperty("SplitDutyVATIndicator")
    private String splitDutyVATIndicator;
}
