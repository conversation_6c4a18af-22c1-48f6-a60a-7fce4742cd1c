package com.hzjm.ups.model.tracking.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Activity {
    @ApiModelProperty(value = "地点")
    private Location location;

    @ApiModelProperty(value = "状态")
    private Status status;

    @ApiModelProperty(value = "活动日期")
    private String date;

    @ApiModelProperty(value = "活动时间")
    private String time;

    @ApiModelProperty(value = "gmt 日期")
    private String gmtDate;

    @ApiModelProperty(value = "gmt偏移量")
    private String gmtOffset;

    @ApiModelProperty(value = "gmt 时间")
    private String gmtTime;
}
