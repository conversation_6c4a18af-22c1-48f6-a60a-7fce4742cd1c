package com.hzjm.ups.model.auth.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OAuthResponse {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("token_type")
    private String tokenType;

    /**
     * 所请求令牌的过期时间（以秒为单位）
     * 目前时 4个小时有效期
     */
    @JsonProperty("expires_in")
    private String expiresIn;

    @JsonProperty("issued_at")
    private String issuedAt;

    @JsonProperty("client_id")
    private String clientId;

    @JsonProperty("scope")
    private String scope;

    @JsonProperty("refresh_count")
    private String refreshCount;

    @JsonProperty("status")
    private String status;
}
