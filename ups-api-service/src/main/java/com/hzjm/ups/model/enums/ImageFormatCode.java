package com.hzjm.ups.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 打印确定收据格式的代码枚举
 */
@Getter
@RequiredArgsConstructor
public enum ImageFormatCode {
    EPL("EPL", "EPL格式"),
    SPL("SPL", "SPL格式"),
    ZPL("ZPL", "ZPL格式"),
    HTML("HTML", "HTML格式");

    private final String code;
    private final String description;

    @JsonValue
    public String getCode() {
        return code;
    }

    public static ImageFormatCode fromCode(String code) {
        for (ImageFormatCode formatCode : values()) {
            if (formatCode.getCode().equals(code)) {
                return formatCode;
            }
        }
        throw new IllegalArgumentException("Invalid image format code: " + code);
    }
}
