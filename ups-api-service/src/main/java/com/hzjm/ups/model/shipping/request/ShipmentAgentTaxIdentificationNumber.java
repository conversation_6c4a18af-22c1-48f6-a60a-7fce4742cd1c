package com.hzjm.ups.model.shipping.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ShipmentAgentTaxIdentificationNumber {
    @JsonProperty("AgentRole")
    private String agentRole;
    
    @JsonProperty("TaxIdentificationNumber")
    private ShipmentTaxIdentificationNumber taxIdentificationNumber;
} 