package com.hzjm.ups.model.transit.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TransitResponseDetail {
    @JsonProperty("Response")
    private Response response;

    @JsonProperty("TransitFromList")
    private List<TransitFrom> transitFromList;

    @JsonProperty("ServiceSummary")
    private List<ServiceSummary> serviceSummary;
} 