package com.hzjm.ups.model.shipping.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hzjm.ups.model.enums.ShipmentIndicationTypeCode;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ShipmentIndicationType {
    @JsonProperty("Code")
    private ShipmentIndicationTypeCode code;

    @JsonProperty("Description")
    private String description;
}
