package com.hzjm.ups.model.address.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AddressClassification {
    @ApiModelProperty("分类代码")
    @JsonProperty("Code")
    private String code;

    @ApiModelProperty("分类描述")
    @JsonProperty("Description")
    private String description;
}
