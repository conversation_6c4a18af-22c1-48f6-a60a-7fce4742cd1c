package com.hzjm.ups.model.rate.request;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RateRequest {
    @JsonProperty("RateRequest")
    @ApiModelProperty(value = "估算运费请求参数",required = true)
    private RateRequestWrapper rateRequest;

    @ApiModelProperty("packageName")
    @JSONField(serialize = false)
    private String packageName;

    // 默认构造函数
    public RateRequest() {
    }

    // 普通构造函数
    public RateRequest(RateRequestWrapper rateRequest) {
        this.rateRequest = rateRequest;
    }

    // 深拷贝构造函数
    public RateRequest(RateRequest source) {
        if (source != null && source.getRateRequest() != null) {
            this.rateRequest = JSON.parseObject(JSON.toJSONString(source.getRateRequest()), RateRequestWrapper.class);
        }
    }

    // 深拷贝方法
    public RateRequest deepCopy() {
        return new RateRequest(this);
    }

}
