package com.hzjm.ups.model.landedcost.request.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

@Getter
@ApiModel(description = "重量单位")
public enum WeightUnit {

    KG("KG", "千克"),
    LB("LB", "磅");

    private final String code;
    private final String description;

    WeightUnit(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }
}
