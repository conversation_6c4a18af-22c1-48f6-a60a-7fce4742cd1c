package com.hzjm.ups.model.shipping.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.hzjm.ups.model.enums.UnitOfMeasurementCode;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ShipmentDryIceWeight {
    @JsonProperty("UnitOfMeasurement")
    private UnitOfMeasurementCode unitOfMeasurement;

    @JsonProperty("Weight")
    private String weight;
}
