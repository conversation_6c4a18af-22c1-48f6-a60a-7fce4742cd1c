package com.hzjm.ups.model.landedcost.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.annotations.ApiModel;
import lombok.Getter;

@Getter
@ApiModel(description = "国家/地区代码")
public enum CountryCode {
    // 常用国家
    US("US", "美国"),
    CN("CN", "中国大陆"),
    HK("HK", "中国香港"),
    TW("TW", "中国台湾"),
    JP("JP", "日本"),
    KR("KR", "韩国"),
    GB("GB", "英国"),
    DE("DE", "德国"),
    FR("FR", "法国"),
    IT("IT", "意大利"),
    
    // A开头的国家
    AF("AF", "阿富汗"),
    AX("AX", "奥兰群岛"),
    AL("AL", "阿尔巴尼亚"),
    DZ("DZ", "阿尔及利亚"),
    AS("AS", "美属萨摩亚"),
    AD("AD", "安道尔"),
    AO("AO", "安哥拉"),
    AI("AI", "安圭拉"),
    AQ("AQ", "南极洲"),
    AG("AG", "安提瓜和巴布达"),
    AR("AR", "阿根廷"),
    AM("AM", "亚美尼亚"),
    AW("AW", "阿鲁巴岛"),
    AU("AU", "澳大利亚"),
    AT("AT", "奥地利"),
    AZ("AZ", "阿塞拜疆"),
    
    // B开头的国家
    BS("BS", "巴哈马"),
    BH("BH", "巴林"),
    BD("BD", "孟加拉国"),
    BB("BB", "巴巴多斯"),
    BY("BY", "白俄罗斯"),
    BE("BE", "比利时"),
    BZ("BZ", "伯利兹"),
    BJ("BJ", "贝宁"),
    BM("BM", "百慕大"),
    BT("BT", "不丹"),
    BO("BO", "玻利维亚"),
    BA("BA", "波斯尼亚和黑塞哥维那"),
    BW("BW", "博茨瓦纳"),
    BR("BR", "巴西"),
    BN("BN", "文莱"),
    BG("BG", "保加利亚"),
    BF("BF", "布基纳法索"),
    BI("BI", "布隆迪"),
    
    // C开头的国家
    KH("KH", "柬埔寨"),
    CM("CM", "喀麦隆"),
    CA("CA", "加拿大"),
    CV("CV", "佛得角"),
    KY("KY", "开曼群岛"),
    CF("CF", "中非共和国"),
    TD("TD", "乍得"),
    CL("CL", "智利"),
    CO("CO", "哥伦比亚"),
    KM("KM", "科摩罗"),
    CG("CG", "刚果"),
    CD("CD", "刚果民主共和国"),
    CK("CK", "库克群岛"),
    CR("CR", "哥斯达黎加"),
    CI("CI", "科特迪瓦"),
    HR("HR", "克罗地亚"),
    CU("CU", "古巴"),
    CY("CY", "塞浦路斯"),
    CZ("CZ", "捷克共和国"),
    
    // D开头的国家
    DK("DK", "丹麦"),
    DJ("DJ", "吉布提"),
    DM("DM", "多米尼加"),
    DO("DO", "多明尼加共和国"),

    // E开头的国家
    EC("EC", "厄瓜多尔"),
    EG("EG", "埃及"),
    SV("SV", "萨尔瓦多"),
    GQ("GQ", "赤道几内亚"),
    ER("ER", "厄立特里亚"),
    EE("EE", "爱沙尼亚"),
    ET("ET", "埃塞俄比亚"),

    // F开头的国家
    FK("FK", "福克兰群岛"),
    FO("FO", "法罗群岛"),
    FJ("FJ", "斐济"),
    FI("FI", "芬兰"),
    
    // G开头的国家
    GA("GA", "加蓬"),
    GM("GM", "冈比亚"),
    GE("GE", "格鲁吉亚"),
    GH("GH", "加纳"),
    GI("GI", "直布罗陀"),
    GR("GR", "希腊"),
    GL("GL", "格陵兰"),
    GD("GD", "格林纳达"),
    GP("GP", "瓜德罗普"),
    GU("GU", "关岛"),
    GT("GT", "危地马拉"),
    GN("GN", "几内亚"),
    GW("GW", "几内亚比绍"),
    GY("GY", "圭亚那"),

    // H开头的国家
    HT("HT", "海地"),
    HN("HN", "洪都拉斯"),
    HU("HU", "匈牙利"),

    // I开头的国家
    IS("IS", "冰岛"),
    IN("IN", "印度"),
    ID("ID", "印度尼西亚"),
    IR("IR", "伊朗"),
    IQ("IQ", "伊拉克"),
    IE("IE", "爱尔兰"),
    IL("IL", "以色列"),

    // J开头的国家
    JM("JM", "牙买加"),
    JO("JO", "约旦"),

    // K开头的国家
    KZ("KZ", "哈萨克斯坦"),
    KE("KE", "肯尼亚"),
    KI("KI", "基里巴斯"),
    KP("KP", "朝鲜"),
    KW("KW", "科威特"),
    KG("KG", "吉尔吉斯斯坦"),

    // L开头的国家
    LA("LA", "老挝"),
    LV("LV", "拉脱维亚"),
    LB("LB", "黎巴嫩"),
    LS("LS", "莱索托"),
    LR("LR", "利比里亚"),
    LY("LY", "利比亚"),
    LI("LI", "列支敦士登"),
    LT("LT", "立陶宛"),
    LU("LU", "卢森堡"),

    // M开头的国家
    MO("MO", "中国澳门"),
    MG("MG", "马达加斯加"),
    MW("MW", "马拉维"),
    MY("MY", "马来西亚"),
    MV("MV", "马尔代夫"),
    ML("ML", "马里"),
    MT("MT", "马耳他"),
    MH("MH", "马绍尔群岛"),
    MQ("MQ", "马提尼克"),
    MR("MR", "毛里塔尼亚"),
    MU("MU", "毛里求斯"),
    MX("MX", "墨西哥"),
    FM("FM", "密克罗尼西亚"),
    MD("MD", "摩尔多瓦"),
    MC("MC", "摩纳哥"),
    MN("MN", "蒙古"),
    ME("ME", "黑山"),
    MA("MA", "摩洛哥"),
    MZ("MZ", "莫桑比克"),
    MM("MM", "缅甸"),

    // N开头的国家
    NA("NA", "纳米比亚"),
    NR("NR", "瑙鲁"),
    NP("NP", "尼泊尔"),
    NL("NL", "荷兰"),
    NC("NC", "新喀里多尼亚"),
    NZ("NZ", "新西兰"),
    NI("NI", "尼加拉瓜"),
    NE("NE", "尼日尔"),
    NG("NG", "尼日利亚"),
    NO("NO", "挪威"),

    // O开头的国家
    OM("OM", "阿曼"),

    // P开头的国家
    PK("PK", "巴基斯坦"),
    PW("PW", "帕劳"),
    PS("PS", "巴勒斯坦"),
    PA("PA", "巴拿马"),
    PG("PG", "巴布亚新几内亚"),
    PY("PY", "巴拉圭"),
    PE("PE", "秘鲁"),
    PH("PH", "菲律宾"),
    PL("PL", "波兰"),
    PT("PT", "葡萄牙"),
    PR("PR", "波多黎各"),

    // Q开头的国家
    QA("QA", "卡塔尔"),

    // R开头的国家
    RE("RE", "留尼汪"),
    RO("RO", "罗马尼亚"),
    RU("RU", "俄罗斯"),
    RW("RW", "卢旺达"),

    // S开头的国家
    BL("BL", "圣巴泰勒米"),
    KN("KN", "圣基茨和尼维斯"),
    LC("LC", "圣卢西亚"),
    VC("VC", "圣文森特和格林纳丁斯"),
    WS("WS", "萨摩亚"),
    SM("SM", "圣马力诺"),
    ST("ST", "圣多美和普林西比"),
    SA("SA", "沙特阿拉伯"),
    SN("SN", "塞内加尔"),
    RS("RS", "塞尔维亚"),
    SC("SC", "塞舌尔"),
    SL("SL", "塞拉利昂"),
    SG("SG", "新加坡"),
    SK("SK", "斯洛伐克"),
    SI("SI", "斯洛文尼亚"),
    SB("SB", "所罗门群岛"),
    SO("SO", "索马里"),
    ZA("ZA", "南非"),
    ES("ES", "西班牙"),
    LK("LK", "斯里兰卡"),
    SD("SD", "苏丹"),
    SR("SR", "苏里南"),
    SZ("SZ", "斯威士兰"),
    SE("SE", "瑞典"),
    CH("CH", "瑞士"),
    SY("SY", "叙利亚"),

    // T开头的国家
    TJ("TJ", "塔吉克斯坦"),
    TZ("TZ", "坦桑尼亚"),
    TH("TH", "泰国"),
    TL("TL", "东帝汶"),
    TG("TG", "多哥"),
    TO("TO", "汤加"),
    TT("TT", "特立尼达和多巴哥"),
    TN("TN", "突尼斯"),
    TR("TR", "土耳其"),
    TM("TM", "土库曼斯坦"),
    TC("TC", "特克斯和凯科斯群岛"),
    TV("TV", "图瓦卢"),

    // U开头的国家
    UG("UG", "乌干达"),
    UA("UA", "乌克兰"),
    AE("AE", "阿联酋"),
    UY("UY", "乌拉圭"),
    UZ("UZ", "乌兹别克斯坦"),

    // V开头的国家
    VU("VU", "瓦努阿图"),
    VA("VA", "梵蒂冈"),
    VE("VE", "委内瑞拉"),
    VN("VN", "越南"),
    VG("VG", "英属维尔京群岛"),
    VI("VI", "美属维尔京群岛"),

    // Y开头的国家
    YE("YE", "也门"),

    // Z开头的国家
    ZM("ZM", "赞比亚"),
    ZW("ZW", "津巴布韦");

    private final String code;
    private final String description;

    CountryCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @JsonValue
    public String getCode() {
        return code;
    }
}
