package com.hzjm.ups.model.recovery.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LabelRecoveryResponseDetail {
    @JsonProperty("Response")
    private Response response;

    @JsonProperty("ShipmentIdentificationNumber")
    private String shipmentIdentificationNumber;

    @JsonProperty("LabelResults")
    private LabelResults labelResults;
} 