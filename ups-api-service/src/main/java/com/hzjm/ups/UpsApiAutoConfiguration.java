package com.hzjm.ups;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzjm.ups.config.LoggingInterceptor;
import com.hzjm.ups.config.UpsConfig;
import com.hzjm.ups.service.ApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
@RequiredArgsConstructor
@ComponentScan(basePackages = "com.hzjm.ups")
@EnableConfigurationProperties(UpsConfig.class)
public class UpsApiAutoConfiguration {
    private final UpsConfig upsConfig;
    private final ObjectMapper objectMapper;
    private final LoggingInterceptor loggingInterceptor;

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .addInterceptor(loggingInterceptor)  // 使用注入的实例
                .connectTimeout(upsConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(upsConfig.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(upsConfig.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .build();
    }

    @Bean
    public Retrofit retrofit(OkHttpClient okHttpClient) {
        return new Retrofit.Builder()
                .baseUrl(upsConfig.getBaseUrl())
                .client(okHttpClient)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .build();
    }

    @Bean
    public ApiService apiService(Retrofit retrofit) {
        return retrofit.create(ApiService.class);
    }
}
