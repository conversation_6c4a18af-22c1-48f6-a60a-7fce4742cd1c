package com.hzjm.ware.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.ShopPack;
import com.hzjm.service.model.DTO.ShopPackPageDto;
import com.hzjm.service.model.DTO.ShopPackSaveDto;
import com.hzjm.service.model.VO.ShopPackCountVo;
import com.hzjm.service.model.VO.ShopPackListVo;
import com.hzjm.service.model.VO.ShopPackVo;
import com.hzjm.service.service.IShopPackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * 预报包裹 前端控制器
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Api(tags = "预报包裹")
@Slf4j
@RestController
@RequestMapping("/shop_pack")
public class ShopPackController {

    @Autowired
    private IShopPackService iShopPackService;

    @ApiOperation("查看预报包裹")
    @GetMapping("/get")
    public HttpResult<ShopPackVo> get(@RequestParam(required = false) Integer id,
                                      @RequestParam(required = false) String logNo) {
        return HttpResult.ok(iShopPackService.getDetail(id, logNo));
    }

    @ApiOperation("预报包裹数量统计")
    @PostMapping("/count")
    public HttpResult<ShopPackCountVo> count(@RequestBody ShopPackPageDto dto) {
        dto.setCurrent(0);
        dto.setSize(0);
        ShopPackCountVo vo = new ShopPackCountVo();

        ShopPackPageDto dto0 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto0);
        dto0.setStatus(null);
        vo.setNum((int) iShopPackService.searchList(dto0).getTotal());

        ShopPackPageDto dto1 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setStatus(1);
        vo.setNum1((int) iShopPackService.searchList(dto1).getTotal());

        ShopPackPageDto dto2 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto2);
        dto2.setStatus(2);
        vo.setNum2((int) iShopPackService.searchList(dto2).getTotal());

        ShopPackPageDto dto3 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto3);
        dto3.setStatus(3);
        vo.setNum3((int) iShopPackService.searchList(dto3).getTotal());

        ShopPackPageDto dto4 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto4);
        dto4.setStatus(4);
        vo.setNum4((int) iShopPackService.searchList(dto4).getTotal());

        return HttpResult.ok(vo);
    }

    @ApiOperation("查询预报包裹列表")
    @PostMapping("/list")
    public HttpResult<IPage<ShopPackListVo>> list(@RequestBody ShopPackPageDto dto) {
        return HttpResult.ok(iShopPackService.searchList(dto));
    }

}