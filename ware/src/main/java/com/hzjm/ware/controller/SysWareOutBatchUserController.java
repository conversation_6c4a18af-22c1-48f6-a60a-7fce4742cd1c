package com.hzjm.ware.controller;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.HttpResult;
import com.hzjm.service.entity.SysFile;
import com.hzjm.service.service.ISysFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 出库批次任务情况 前端控制器
 *
 */
@Api(tags = "出库批次任务情况")
@Slf4j
@RestController
@RequestMapping("/sys_ware_out_batch_user")
public class SysWareOutBatchUserController {

    @Resource
    ISysFileService iSysFileService;
    @ApiOperation("查询出库批次的附件信息")
    @GetMapping("/getFile")
    public HttpResult<List<SysFile>> getFile(@RequestParam Integer id) {
        if (ObjectUtils.isEmpty(id)){
            return HttpResult.error("id is not null");
        }

        List<SysFile> fileUrlList = iSysFileService.list(Wrappers.<SysFile>lambdaQuery()
                .eq(SysFile::getRelationType, SysConstants.SYS_FILE_TYPE_7)
                .eq(SysFile::getRelationId, id));

        return HttpResult.ok(fileUrlList);
    }

}
