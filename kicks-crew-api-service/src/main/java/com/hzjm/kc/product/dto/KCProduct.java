package com.hzjm.kc.product.dto;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;

/**
 * {
 *         "modelNo": "DZ4131-600",
 *         "sizes": {
 *             "us": "7",
 *             "uk": "7",
 *             "eu": "40"
 *         },
 *         "currency": "USD",
 *         "price": 182,
 *         "sku": "DZ4131-600-70"
 *     },
 */
@Data
public class KCProduct implements Serializable {
    private static final long serialVersionUID = 1L; // 推荐添加 serialVersionUID
    String modelNo;
    Sizes sizes;
    String currency;
    Integer price;
    String sku;

    @Data
    public static class Sizes implements Serializable {
        private static final long serialVersionUID = 1L; // 推荐添加 serialVersionUID
        String us;
        String uk;
        String eu;
    }
}
