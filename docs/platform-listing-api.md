# 平台寄售单查询接口文档

## 接口概述

根据您提供的SQL查询需求，我们创建了一个外部访问接口来查询platform_listing表数据，支持分页参数。

原始SQL查询：
```sql
SELECT * FROM `platform_listing` 
WHERE `sale_status` = 'INACTIVE' 
  AND `platform` = 'B2B_SHOP' 
  AND `account` = 'B2B_SHOP' 
ORDER BY `id` DESC;
```

## 接口信息

- **接口路径**: `/api/system/query_platform_listings`
- **请求方法**: POST
- **Content-Type**: application/json

## 请求参数

### PlatformListingQueryDto

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 页码，从1开始，默认1 |
| pageSize | Integer | 否 | 每页数据量，默认25 |
| saleStatus | String | 否 | 销售状态，可选值：ACTIVE, INACTIVE, PENDING, CANCELED, MATCHED, COMPLETED等 |
| platform | String | 否 | 平台，可选值：STOCK_X, GOAT_STV, KICKS_CREW, EBAY, POIZON, TIKTOK_SHOP, B2B_SHOP |
| account | String | 否 | 账号，可选值：STOCK_X_CONSIGNMENT, GOAT_STV_CONSIGNMENT, KICKS_CREW_CONSIGNMENT, EBAY_CONSIGNMENT, POIZON_CONSIGNMENT, TIKTOK_SHOP, B2B_SHOP |
| beginTime | String | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | 否 | 结束时间，格式：yyyy-MM-dd HH:mm:ss |

### 请求示例

```json
{
  "current": 1,
  "pageSize": 20,
  "saleStatus": "INACTIVE",
  "platform": "B2B_SHOP",
  "account": "B2B_SHOP"
}
```

## 响应结果

### 成功响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 12345,
        "gmtCreate": "2025-02-26T10:30:00",
        "gmtModify": "2025-02-26T10:30:00",
        "knetListingId": "knet_123456",
        "shopUserId": "shop_user_001",
        "oneId": "one_id_001",
        "platformListingId": "platform_listing_001",
        "orderId": "order_001",
        "batchId": "batch_001",
        "listingPrice": 15000,
        "salePrice": 14500,
        "saleStatus": "INACTIVE",
        "templateId": "template_001",
        "variantId": "variant_001",
        "knetId": "knet_001",
        "autoPricingEnable": true,
        "bottomLine": 13000,
        "account": "B2B_SHOP",
        "platform": "B2B_SHOP"
      }
    ],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "message": "查询失败: 具体错误信息",
  "data": null
}
```

## 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 主键ID |
| gmtCreate | DateTime | 创建时间 |
| gmtModify | DateTime | 修改时间 |
| knetListingId | String | Knet平台的ListingId |
| shopUserId | String | 商户Id |
| oneId | String | One Id |
| platformListingId | String | 对应平台上的真实寄售单Id |
| orderId | String | 订单Id |
| batchId | String | 批次Id |
| listingPrice | Integer | 用户listing出价（美分单位） |
| salePrice | Integer | 寄售商品出价（美分单位） |
| saleStatus | String | 平台Listing状态 |
| templateId | String | 商品模板Id |
| variantId | String | 商品变体Id |
| knetId | String | 寄售商品属性对应的Knet平台Id |
| autoPricingEnable | Boolean | 是否打开AutoPricing |
| bottomLine | Integer | 底线价（美分单位） |
| account | String | 所属账号 |
| platform | String | 寄售单所属平台 |

## 使用示例

### cURL 请求示例

```bash
curl -X POST "http://localhost:8080/api/system/query_platform_listings" \
  -H "Content-Type: application/json" \
  -d '{
    "current": 1,
    "pageSize": 10,
    "saleStatus": "INACTIVE",
    "platform": "B2B_SHOP",
    "account": "B2B_SHOP"
  }'
```

### Java 客户端示例

```java
PlatformListingQueryDto request = new PlatformListingQueryDto();
request.setCurrent(1);
request.setPageSize(10);
request.setSaleStatus(ListingStatus.INACTIVE);
request.setPlatform(SourcePlatform.B2B_SHOP);
request.setAccount(ListingAccount.B2B_SHOP);

HttpResult<IPage<PlatformListingQueryVo>> response = apiSystemProvider.queryPlatformListings(request);
```

## 注意事项

1. 查询结果按ID降序排列，与原始SQL保持一致
2. 分页参数可选，不传则返回所有符合条件的数据
3. 所有查询条件都是可选的，可以根据需要组合使用
4. 时间查询支持范围查询（beginTime到endTime）
5. 价格字段以美分为单位存储
6. 枚举字段在响应中以字符串形式返回
