package com.hzjm.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.model.HttpResult;
import com.hzjm.service.entity.SysProdSwitch;
import com.hzjm.service.model.DTO.PageBaseSearchDto;
import com.hzjm.service.model.DTO.SysProdSwitchItemPageDto;
import com.hzjm.service.model.DTO.SysProdSwitchSaveDto;
import com.hzjm.service.model.VO.SysProdSwitchItemCountVo;
import com.hzjm.service.model.VO.SysProdSwitchItemListVo;
import com.hzjm.service.service.ISysProdSwitchItemService;
import com.hzjm.service.service.ISysProdSwitchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 转仓 前端控制器
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Api(tags = "转仓")
@Slf4j
@RestController
@RequestMapping("/sys_prod_switch")
public class SysProdSwitchController {

    @Autowired
    private ISysProdSwitchService iSysProdSwitchService;

    @Autowired
    private ISysProdSwitchItemService iSysProdSwitchItemService;

    @ApiOperation(value = "保存转仓", notes = "说明：\n" +
            "添加：不传id和delFlag\n" +
            "修改：传id，不传delFlag\n" +
            "删除：传id，delFlag固定传-1")
    @PostMapping("/save")
    public HttpResult<Boolean> save(@RequestBody SysProdSwitchSaveDto dto) {
        SysProdSwitch entity = new SysProdSwitch();
        BeanUtils.copyProperties(dto, entity);

        return HttpResult.ok(iSysProdSwitchService.saveSysProdSwitch(entity));
    }

    @ApiOperation("查询转仓商品统计")
    @PostMapping("/count")
    @TrimParam
    public HttpResult<SysProdSwitchItemCountVo> count(@RequestBody SysProdSwitchItemPageDto dto) {
        return HttpResult.ok(iSysProdSwitchItemService.getCount(dto));
    }

    @ApiOperation("查询转仓商品列表")
    @PostMapping("/listItem")
    @TrimParam
    public HttpResult<IPage<SysProdSwitchItemListVo>> list(@RequestBody SysProdSwitchItemPageDto dto) {
        return HttpResult.ok(iSysProdSwitchItemService.searchList(dto));
    }

    @ApiOperation("批量取消")
    @PostMapping("/batchDeal")
    public HttpResult<Boolean> batchDeal(@RequestBody PageBaseSearchDto dto) {
        return HttpResult.ok(iSysProdSwitchItemService.release(dto.getIdList()));
    }

}
