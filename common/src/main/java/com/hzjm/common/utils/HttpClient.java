package com.hzjm.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.model.BaseException;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.util.ObjectUtils;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.util.Date;

import com.hzjm.common.utils.DateTimeUtils;

import java.util.HashMap;
import java.util.Map;

public class HttpClient {

    public static String doGet(String url, Map<String, String> headers) {
        try (CloseableHttpClient httpClient = createAcceptSelfSignedCertificateClient()) {
            HttpGet httpget = new HttpGet(url);

            RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(10000)// 连接主机服务超时时间
                    .setConnectionRequestTimeout(15000)// 请求超时时间
                    .setSocketTimeout(25000)// 数据读取超时时间
                    .build();
            httpget.setConfig(requestConfig);

            if (!ObjectUtils.isEmpty(headers)) {
                headers.keySet().forEach(key -> {
                    httpget.setHeader(key, headers.get(key));
                });
            }

            HttpResponse response = httpClient.execute(httpget);
//            return getContext(response.getEntity());    //直接返回调用结果
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                return getContext(response.getEntity());
            } else if (statusCode == 403) {
                JSONObject rs = new JSONObject();
                rs.put("status", "fail");
                rs.put("msg", "未获得Touch平台的访问权限");
                return rs.toJSONString();
            }
        } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException | IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public static String doPost(String path, String postContent, Map<String, String> headers) {
        URL url = null;
        try {
            url = new URL(path);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod("POST");// 提交模式
            httpURLConnection.setConnectTimeout(10000);//连接超时 单位毫秒
            httpURLConnection.setReadTimeout(10000);//读取超时 单位毫秒

            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            httpURLConnection.setRequestProperty("Content-Type", "application/json");
            if (!ObjectUtils.isEmpty(headers)) {
                headers.keySet().forEach(key -> {
                    httpURLConnection.setRequestProperty(key, headers.get(key));
                });
            }

            httpURLConnection.connect();
            OutputStream os = httpURLConnection.getOutputStream();
            os.write(postContent.getBytes("UTF-8"));
            os.flush();

            StringBuilder sb = new StringBuilder();
            int httpRspCode = httpURLConnection.getResponseCode();
            if (httpRspCode == HttpURLConnection.HTTP_OK) {
                // 开始获取数据
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(httpURLConnection.getInputStream(), "utf-8"));
                String line = null;
                while ((line = br.readLine()) != null) {
                    sb.append(line);
                }
                br.close();
                return sb.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String doJsonMethod(String method, String path, String postContent, Map<String, String> headers) {
        URL url = null;
        try {
            url = new URL(path);
            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
            httpURLConnection.setRequestMethod(method);// 提交模式
            httpURLConnection.setConnectTimeout(30000);//连接超时 单位毫秒
            httpURLConnection.setReadTimeout(30000);//读取超时 单位毫秒

            // 发送POST请求必须设置如下两行
            httpURLConnection.setDoOutput(true);
            httpURLConnection.setDoInput(true);
            httpURLConnection.setRequestProperty("Charset", "UTF-8");
            httpURLConnection.setRequestProperty("Content-Type", "application/json");
            if (!ObjectUtils.isEmpty(headers)) {
                headers.keySet().forEach(key -> {
                    httpURLConnection.setRequestProperty(key, headers.get(key));
                });
            }

            httpURLConnection.connect();
            OutputStream os = httpURLConnection.getOutputStream();
            os.write(postContent.getBytes("UTF-8"));
            os.flush();

            StringBuilder sb = new StringBuilder();
            int httpRspCode = httpURLConnection.getResponseCode();
            if (httpRspCode == HttpURLConnection.HTTP_OK) {
                // 开始获取数据
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(httpURLConnection.getInputStream(), "utf-8"));
                String line = null;
                while ((line = br.readLine()) != null) {
                    sb.append(line);
                }
                br.close();
                return sb.toString();
            } else if (httpRspCode == 400) {
                // 开始获取数据
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(httpURLConnection.getErrorStream(), "utf-8"));
                String line = null;
                while ((line = br.readLine()) != null) {
                    sb.append(line);
                }
                br.close();
                return sb.toString();
            } else if (httpRspCode == 403) {
                JSONObject rs = new JSONObject();
                rs.put("status", "fail");
                rs.put("msg", "未获得Touch平台的访问权限");
                return rs.toJSONString();
            } else {
                System.out.println("未知的状态码："+httpRspCode);

                // 开始获取数据
                BufferedReader br = new BufferedReader(
                        new InputStreamReader(httpURLConnection.getErrorStream(), "utf-8"));
                String line = null;
                while ((line = br.readLine()) != null) {
                    sb.append(line);
                }
                br.close();
                return sb.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String doForm(String url, Map<String, Object> params) {
        try {
            CloseableHttpClient httpClient = HttpClientBuilder.create().build();

            HttpPost httpPost = new HttpPost(url);

            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            if (params != null) {
                ContentType contentType = ContentType.create("text/plain", Consts.UTF_8);
                for (String key : params.keySet()) {
                    if (key.contains("Img")) {
                        builder.addPart(key, new FileBody((File) params.get(key)));
                    } else {
                        builder.addPart(key, new StringBody(params.get(key).toString(), contentType));
                    }
                }
            }
            HttpEntity entity = builder.build();
            httpPost.setHeader(HTTP.CONTENT_TYPE, entity.getContentType().getValue());
            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);
            String result = EntityUtils.toString(response.getEntity(), "utf-8");
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BaseException("发起请求[" + url + "]时，出现系统错误：" + e.getMessage());
        }
    }

    /**
     * 绕过https的ssl认证
     */
    private static CloseableHttpClient createAcceptSelfSignedCertificateClient()
            throws KeyManagementException, NoSuchAlgorithmException, KeyStoreException {

        //采用绕过验证的方式处理https请求
        SSLContext sslcontext = createIgnoreVerifySSL();

        //设置协议http和https对应的处理socket链接工厂的对象
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.INSTANCE)
                .register("https", new SSLConnectionSocketFactory(sslcontext))
                .build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        HttpClients.custom().setConnectionManager(connManager);

        // finally create the HttpClient using HttpClient factory methods and assign the ssl socket factory
        return HttpClients
                .custom()
                .setConnectionManager(connManager)
                .build();
    }

    private static String getContext(HttpEntity httpEntity) throws IOException {
        BufferedReader bufferedReader = new BufferedReader
                (new InputStreamReader(httpEntity.getContent(), "UTF-8"), 8 * 1024);
        String line = null;
        StringBuilder sb = new StringBuilder();
        while ((line = bufferedReader.readLine()) != null) {
            sb.append(line + '\n');
        }
        return sb.toString();
    }

    /**
     * 绕过验证
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    public static SSLContext createIgnoreVerifySSL() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sc = SSLContext.getInstance("SSLv3");

        // 实现一个X509TrustManager接口，用于绕过验证，不用修改里面的方法
        X509TrustManager trustManager = new X509TrustManager() {
            @Override
            public void checkClientTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public void checkServerTrusted(
                    java.security.cert.X509Certificate[] paramArrayOfX509Certificate,
                    String paramString) throws CertificateException {
            }

            @Override
            public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                return null;
            }
        };

        sc.init(null, new TrustManager[]{trustManager}, null);
        return sc;
    }

    public static String getQrCode(String access_token, String scene, String pageUrl) {
        if (access_token != null) {
            try {
                JSONObject params = new JSONObject();
                params.put("scene", scene);
//                params.put("width", 430);
                params.put("page", pageUrl);

                CloseableHttpClient httpClient = HttpClientBuilder.create().build();

                HttpPost httpPost = new HttpPost("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + access_token);
                httpPost.addHeader(HTTP.CONTENT_TYPE, "application/json");
                String body = params.toJSONString();
                StringEntity entity;
                entity = new StringEntity(body);
                entity.setContentType("image/png");
                httpPost.setEntity(entity);
                HttpResponse response;
                response = httpClient.execute(httpPost);
                InputStream inputStream = response.getEntity().getContent();

                params.put("access_token", access_token);
                // 文件上传
                return AwsS3Utils.uploadFile(inputStream, "qrcode/" + DateTimeUtils.getNow().getTime() + ".jpg");
                // return AliyunOSSUtils.uploadFile(inputStream, "qrcode/" + DateTimeUtils.getNow().getTime() + ".jpg");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}
