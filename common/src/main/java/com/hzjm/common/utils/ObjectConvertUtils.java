package com.hzjm.common.utils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ObjectConvertUtils {
    /**
     *  利用 Java 反射 将对象转换成 Map 类型
     * @param obj
     * @return
     */
    public static Map<String, Object> objectToMap(Object obj) {
        Map<String, Object> map = new HashMap<>();
        // 获取对象的Class
        Class<?> clazz = obj.getClass();
        // 获取对象的所有字段
        Field[] fields = clazz.getDeclaredFields();
        // 遍历字段并映射到Map中
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            try {
                Object fieldValue = field.get(obj);
                map.put(fieldName, fieldValue);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return map;
    }

    /**
     * 将对象转换成 map 并 过滤掉 为 null 的值
     * @param obj
     * @return
     */
    public static Map<String, Object> objectToMapNonNull(Object obj) {
        Map<String, Object> map = new HashMap<>();
        // 获取对象的Class
        Class<?> clazz = obj.getClass();
        // 获取对象的所有字段
        Field[] fields = clazz.getDeclaredFields();
        // 遍历字段并映射到Map中
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            try {
                Object fieldValue = field.get(obj);
                if (fieldValue != null) { // 只将非null的属性加入map
                    map.put(fieldName, fieldValue);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return map;
    }


    /**
     *  将 Map<String, Object> 状换成 Map<String, String> 类型
     * @param originalMap
     * @return
     */
    public static Map<String, String> convertMap(Map<String, Object> originalMap) {
        Map<String, String> convertedMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : originalMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                // 如果值已经是String类型，则直接添加到转换后的Map中
                convertedMap.put(key, (String) value);
            } else if (value != null) {
                // 如果值不为null，则将其转换为String类型并添加到转换后的Map中
                convertedMap.put(key, value.toString());
            }
        }

        return convertedMap;
    }

}
