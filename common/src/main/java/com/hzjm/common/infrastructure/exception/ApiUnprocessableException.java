package com.hzjm.common.infrastructure.exception;

import com.hzjm.common.model.BaseException;

/**
 *  Goat Api 处理的请求 返回422时抛出
 */
public class ApiUnprocessableException extends BaseException {
    public ApiUnprocessableException(int code) {
        super(code);
    }

    public ApiUnprocessableException(String msg) {
        super(msg);
    }

    public ApiUnprocessableException(int code, String msg) {
        super(code, msg);
    }

    public ApiUnprocessableException(int code, Exception e) {
        super(code, e);
    }
}
