package com.hzjm.common.infrastructure.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.hzjm.common.infrastructure.service.httprequest.IHttpRequestService;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.infrastructure.exception.ApiRateLimitException;

import lombok.extern.slf4j.Slf4j;

import org.apache.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URISyntaxException;
import java.util.Map;

@Slf4j
@Service
public class ApiRequestServiceImpl implements IApiRequestService {
    protected static int MAX_RETRIES = 5;
    protected static int SLEEP_TIME_BEFORE_RETRY = 10;

    @Qualifier("httpRequestServiceImpl")
    @Autowired
    IHttpRequestService httpRequestService;

    /**
     * @param request Http Request 的 参数和类型
     * @param <T> 要求返回的Http 请求的实体类型
     * @return http请求的实体结果
     * @throws BaseException
     */
    @Nullable
    @Override
    public <T> T doRequest(HttpRequest<T> request) throws BaseException {
        log.info("doRequest: host: {}, method: {}, path: {}", request.getHost(), request.getMethod(), request.getPath());

        int retries = 0;
        while (retries < MAX_RETRIES) {
            try {
                // 执行 HTTP 请求
                String jsonString = httpRequestService.doRequest(
                        request.getHost(),
                        request.getPath(),
                        request.getMethod().rawValue,
                        request.getQueryParameters(),
                        request.getHeaders(),
                        request.getBody()
                );

                // 根据提供的范型直接序列化对应的对象
                log.debug("Raw Response: " + jsonString);
                T response = JSONObject.parseObject(jsonString, request.getReferenceType());
                if (response != null) {
                    return response;
                }else{
                    throw new BaseException("parse jsonString to" + request.getReferenceType().getType() + "error, origin json string: " + jsonString);
                }

            } catch (IOException e) {
                log.error("I/O error occurred: " + e.getMessage());
                throw new BaseException("I/O error while processing request: " + e.getMessage(), e);
            } catch (URISyntaxException e) {
                log.error("URI syntax error: " + e.getMessage());
                throw new BaseException("Invalid URI: " + e.getMessage(), e);
            } catch (ClassCastException e) {
                log.error("Class cast error: " + e.getMessage());
                throw new BaseException("Class cast error while parsing response: " + e.getMessage(), e);
            } catch (JSONException e) {
                log.error("JSON parsing error: " + e.getMessage());
                throw new BaseException("JSON parsing error while processing response: " + e.getMessage(), e);
            } catch (ApiRateLimitException exception) {
                retries++;
                if (retries >= MAX_RETRIES) {
                    log.error("Max retries reached, stop retrying.");
                    log.error(BaseUtils.getCallStack());
                    throw exception;
                }

                try {
                    Thread.sleep((long) Math.pow(SLEEP_TIME_BEFORE_RETRY, retries));
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }

        log.error(BaseUtils.getCallStack());
        throw new ApiRateLimitException(HttpStatus.SC_TOO_MANY_REQUESTS, "Max retries reached.");
    }

    /**
     * @param request
     * @param token
     * @param <T>
     * @return
     * @throws BaseException
     */
    @Override
    public <T> T doRequest(HttpRequest<T> request, String token) throws BaseException {
        return null;
    }

    /**
     * @param request
     * @param accountEmail
     * @param <T>
     * @return
     * @throws BaseException
     */
    @Override
    public <T> T doRequestWithAccount(HttpRequest<T> request, String accountEmail) throws BaseException {
        return null;
    }

    /**
     * @param urlString
     * @param headers
     * @return
     * @throws BaseException
     */
    @Override
    public byte[] download(String urlString, Map<String, String> headers) throws BaseException {
        try {
            return httpRequestService.downloadFile(urlString, headers);
        }catch (IOException e) {
            String errorInfo = "Download File Error, url: " + urlString;
            log.error(errorInfo);
            throw new BaseException(e.getMessage());
        }
    }

    /**
     * @param request
     * @param file
     * @param <T>
     * @return
     * @throws BaseException
     */
    @Override
    public <T> T uploadFile(HttpRequest<T> request, File file) throws BaseException {
        try {

            Map formFields = JSON.parseObject(request.getBody(), Map.class);

            String jsonString = httpRequestService.uploadFile(
                    request.getHost(),
                    request.getPath(),
                    request.getMethod().rawValue,
                    request.getQueryParameters(),
                    request.getHeaders(),
                    formFields,
                    file
            );

            T response = JSONObject.parseObject(jsonString, request.getReferenceType());
            if (response != null) {
                log.debug("Response: " + response.toString());
                return response;
            }else{
                throw new BaseException("parse jsonString to" + request.getReferenceType().getType() + "error, origin json string: " + jsonString);
            }


        }catch (Exception e) {
            String errorInfo = "Upload File Error, url: " + request.getHost() + request.getPath();
            log.error(errorInfo);
            throw new BaseException(e.getMessage());
        }
    }

}
