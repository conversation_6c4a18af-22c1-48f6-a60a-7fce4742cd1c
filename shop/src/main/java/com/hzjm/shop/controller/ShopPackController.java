package com.hzjm.shop.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.*;
import com.hzjm.service.model.DTO.PageBaseSearchDto;
import com.hzjm.service.model.DTO.ShopPackPageDto;
import com.hzjm.service.model.DTO.ShopPackSaveDto;
import com.hzjm.service.model.VO.ShopPackCountVo;
import com.hzjm.service.model.VO.ShopPackListVo;
import com.hzjm.service.model.VO.ShopPackVo;
import com.hzjm.service.model.touch.TouchProductResponse;
import com.hzjm.service.service.*;
import com.hzjm.service.touch.TouchUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 预报包裹 前端控制器
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Api(tags = "预报包裹")
@Slf4j
@RestController
@RequestMapping("/shop_pack")
public class ShopPackController {

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private IShopPackProdService iShopPackProdService;

    @Autowired
    private ISysWareInService iSysWareInService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private IShopPreService iShopPreService;

    @Autowired
    private TouchUtils touchUtils;

    @ApiOperation("获取sku基本信息")
    @GetMapping("/getFromTouch")
    public HttpResult<TouchProductResponse> getFromTouch(@RequestParam String sku) {
        return HttpResult.ok(touchUtils.getSku(sku).toJavaObject(TouchProductResponse.class));
    }

    @ApiOperation(value = "保存预报包裹", notes = "说明：\n" +
            "添加：不传id和delFlag\n" +
            "修改：传id，不传delFlag\n" +
            "删除：传id，delFlag固定传-1")
    @PostMapping("/save")
    public HttpResult<Boolean> save(@RequestBody ShopPackSaveDto dto) {
        ShopPack entity = new ShopPack();
        BeanUtils.copyProperties(dto, entity);

        // 商家新增预报允许商家认领无主件
        if (ObjectUtils.isEmpty(dto.getId())) {
            entity.setId(iShopPackService.getIdForLogNo(entity));
        }

        return HttpResult.ok(iShopPackService.saveShopPack(entity));
    }

    @ApiOperation("查看预报包裹")
    @GetMapping("/get")
    public HttpResult<ShopPackVo> get(@RequestParam Integer id) {
        return HttpResult.ok(iShopPackService.getDetail(id, null));
    }

    @ApiOperation("预报包裹数量统计")
    @PostMapping("/count")
    public HttpResult<ShopPackCountVo> count(@RequestBody ShopPackPageDto dto) {
        dto.setCurrent(0);
        dto.setSize(0);
        ShopPackCountVo vo = new ShopPackCountVo();

        ShopPackPageDto dto0 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto0);
        dto0.setStatus(null);
        vo.setNum((int) iShopPackService.searchList(dto0).getTotal());

        ShopPackPageDto dto1 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setStatus(1);
        vo.setNum1((int) iShopPackService.searchList(dto1).getTotal());

        ShopPackPageDto dto2 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto2);
        dto2.setStatus(2);
        vo.setNum2((int) iShopPackService.searchList(dto2).getTotal());

        ShopPackPageDto dto3 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto3);
        dto3.setStatus(3);
        vo.setNum3((int) iShopPackService.searchList(dto3).getTotal());

        ShopPackPageDto dto4 = new ShopPackPageDto();
        BeanUtils.copyProperties(dto, dto4);
        dto4.setStatus(4);
        vo.setNum4((int) iShopPackService.searchList(dto4).getTotal());

        return HttpResult.ok(vo);
    }

    @ApiOperation("查询预报包裹列表")
    @PostMapping("/list")
    public HttpResult<IPage<ShopPackListVo>> list(@RequestBody ShopPackPageDto dto) {
        return HttpResult.ok(iShopPackService.searchList(dto));
    }

    @ApiOperation("预报包裹导入模板")
    @GetMapping("/getImportMode")
    public HttpResult<String> getImportMode() {
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        List<String> headers = Arrays.asList(
                "预报类型（必填）", "物流单号（必填）", "SKU（选填）", "尺码（选填）", "数量（选填）", "成本（选填）", "货源（选填）", "仓库（选填）"
        );
        dataList.add(LanguageConfigService.i18nForMsg(headers));

        dataList.add(Arrays.asList(
                "shipping/dropoff", "", "", "", "", "", "", ""
        ));
        dataList.add(Arrays.asList(
                "shipping", "1z434233123", "", "", "", "", "", ""
        ));
        dataList.add(Arrays.asList(
                "dropoff", "", "", "", "", "", "", ""
        ));

        String url = null;
        try {
            url = ExcelReader.generateExcelFile(dataList, LanguageConfigService.i18nForMsg("预报包裹导入模板") + ".xlsx");
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }

        return HttpResult.ok(url);
    }

    @ApiOperation("导入包裹")
    @PostMapping("/import")
    @AcquireTaskLock(name = "importShopPack", timeout = 3, blocking = true)
    public HttpResult<Boolean> importPack(MultipartFile file) {
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        List<String> headers = Arrays.asList(
                // "预报类型（必填）", "物流单号（必填）", "SKU", "尺码", "数量", "成本", "货源", "仓库"
                "type", "logNo", "sku", "spec", "num", "costPrice", "supply", "wareName"
        );
        dataList.add(LanguageConfigService.i18nForMsg(headers));

        JSONArray array = null;
        try {
            array = ExcelReader.readExcel(file.getOriginalFilename(), file.getInputStream(), headers);
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("文件读取异常"));
        }

        if (ObjectUtils.isEmpty(array)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未读取到任意数据"));
        }

        List<SysWare> wareList = iSysWareService.list();
        Map<String, Integer> nameMap = wareList.stream().collect(Collectors.toMap(SysWare::getName, SysWare::getId));

        ShopPack pack = null;
        Map<String, ShopPack> packMap = new HashMap<>();
        for (Object obj : array) {
            JSONObject data = (JSONObject) obj;
            String typeName = data.getString("type");
            if (ObjectUtils.isEmpty(typeName)) {
                // 空数据跳过
                break;
            }
            Integer type = null;
            switch (typeName.trim().toUpperCase(Locale.ROOT)) {
                case "SHIPPING":
                    type = 1;
                    break;
                case "DROPOFF":
                    type = 2;
                    break;
                default:
                    throw new BaseException(LanguageConfigService.i18nForMsg("无法识别的预报类型"));
            }

            String logNo = data.getString("logNo");
            if (type == 1) {
                pack = packMap.get(logNo);
            }
            if (ObjectUtils.isEmpty(pack)) {
                if (ObjectUtils.isEmpty(logNo)) {
                    logNo = "";
                }
                pack = new ShopPack();
                pack.setLogNo(logNo);
                pack.setType(type);
                pack.setWareId(nameMap.get(data.getString("wareName")));
                pack.setProdList(new ArrayList<>());
                packMap.put(logNo, pack);
            }

            Integer num = data.getInteger("num");
            if (ObjectUtils.isEmpty(num)) {
                num = 1;
            }
            for (Integer i = 0; i < num; i++) {
                ShopPackProd prod = new ShopPackProd();
                prod.setSpec(data.getString("spec"));
                prod.setSku(data.getString("sku"));
                prod.setRemarks(data.getString("remarks"));
                prod.setSupply(data.getString("supply"));
                prod.setCostPrice(data.getBigDecimal("costPrice"));
                pack.getProdList().add(prod);
            }
        }

        Integer shopId = JwtContentHolder.getShopId();
        List<ShopPack> packList = new ArrayList<>();
        packMap.keySet().forEach(logNo -> {
            ShopPack data = packMap.get(logNo);
            data.setShopId(shopId);
            data.setNum(data.getProdList().size());
            packList.add(data);
        });

         // 商家端 按照仓库和类型分组
        Map<ShopPackGroupTypeWareId, List<ShopPack>> group = packList.stream().collect(
                Collectors.groupingBy(
                        sp -> new ShopPackGroupTypeWareId(sp.getType(), sp.getWareId())
                )
        );
        group.keySet().forEach(type -> {
            List<ShopPack> itemList = group.get(type);
            ShopPack tmp = itemList.get(0);

            ShopPre pre = new ShopPre();
            pre.setWareId(tmp.getWareId());
            pre.setShopId(tmp.getShopId());
            pre.setType(tmp.getType());
            pre.setPackList(itemList);
            iShopPreService.saveShopPre(pre);
        });

        return HttpResult.ok(true);
    }

    @ApiOperation("导出预报包裹")
    @PostMapping("/export")
    public HttpResult<String> export(@RequestBody ShopPackPageDto dto) {
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        List<String> headers = Arrays.asList(
                "预报时间", "预报批次", "预报类型", "物流单号",
                "品名", "sku", "尺码", "数量", "成本", "货源", "包裹状态", "入库日期", "入库批次", "仓库"
        );
        dataList.add(LanguageConfigService.i18nForMsg(headers));

        List<ShopPackListVo> entityList = iShopPackService.searchList(dto).getRecords();

        Map<Integer, List<ShopPackProd>> prodMap = new HashMap<>();
        Map<Integer, String> batchMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(entityList)) {
            List<Integer> packIdList = entityList.stream().map(ShopPackListVo::getId).collect(Collectors.toList());

            // 包裹商品信息
            List<ShopPackProd> prodList = iShopPackProdService.list(Wrappers.<ShopPackProd>lambdaQuery()
                    .in(ShopPackProd::getPackId, packIdList));
            prodMap.putAll(prodList.stream().collect(Collectors.groupingBy(ShopPackProd::getPackId)));

            // 商品入库信息
            List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getPackId, packIdList));
            if (!ObjectUtils.isEmpty(inProdList)) {
                Map<Integer, List<Integer>> inPackTree = new HashMap<>();
                inProdList.forEach(inProd -> {
                    List<Integer> packIds = inPackTree.get(inProd.getInId());
                    if (ObjectUtils.isEmpty(packIds)) {
                        packIds = new ArrayList<>();
                        inPackTree.put(inProd.getInId(), packIds);
                    }
                    packIds.add(inProd.getPackId());
                });

                List<SysWareIn> inList = iSysWareInService.list(Wrappers.<SysWareIn>lambdaQuery()
                        .in(SysWareIn::getId, inProdList.stream().map(SysWareInProd::getInId).collect(Collectors.toList())));
                inList.forEach(in -> {
                    inPackTree.get(in.getId()).stream().distinct().forEach(packId -> {
                        batchMap.put(packId, in.getBatchNo());
                    });
                });
            }
        }

        // 填充数据
        int i = 1;
        for (ShopPackListVo data : entityList) {
            List<ShopPackProd> prodList = prodMap.get(data.getId());
            if (ObjectUtils.isEmpty(prodList)) {
                prodList = new ArrayList<>();
                ShopPackProd packProd = new ShopPackProd();
                packProd.setSpec("");
                packProd.setSku("");
                packProd.setRemarks("");
                prodList.add(packProd);
            }
            prodList.forEach(prod -> {
                List<String> cowList = new ArrayList<>();

                cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtCreate()));
                cowList.add(BaseUtils.covertString(data.getBatchNo()));
                switch (data.getType()) {
                    case 1:
                        cowList.add("SHIPPING");
                        break;
                    case 2:
                        cowList.add("DROPOFF");
                    case 3:
                        cowList.add("RETURNED");
                        break;
                    default:
                        cowList.add("");
                }
                cowList.add(BaseUtils.covertString(data.getLogNo()));
                cowList.add(BaseUtils.covertString(prod.getRemarks()));
                cowList.add(BaseUtils.covertString(prod.getSku()));
                cowList.add(BaseUtils.covertString(prod.getSpec()));
                cowList.add("1");
                cowList.add(BaseUtils.covertString(prod.getCostPrice()));
                cowList.add(BaseUtils.covertString(prod.getSupply()));

                // 状态，1-已预约，2-已入库，3-平台打回，4-已查验
                switch (data.getStatus()) {
                    case 1:
                        cowList.add(LanguageConfigService.i18nForMsg("已预约"));
                        break;
                    case 2:
                        cowList.add(LanguageConfigService.i18nForMsg("已入库"));
                        break;
                    case 3:
                        cowList.add(LanguageConfigService.i18nForMsg("平台打回"));
                        break;
                    case 4:
                        cowList.add(LanguageConfigService.i18nForMsg("已查验"));
                        break;
                    default:
                        cowList.add("");
                }
                cowList.set(cowList.size() - 1, LanguageConfigService.i18nForMsg(cowList.get(cowList.size() - 1)));

                cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtWare()));
                cowList.add(BaseUtils.covertString(batchMap.get(prod.getId())));
                cowList.add(BaseUtils.covertString(data.getWareName()));

                dataList.add(cowList);
            });

            i = i + 1;
        }

        String url = null;
        try {
            url = ExcelReader.generateExcelFile(dataList,
                    LanguageConfigService.i18nForMsg("预报包裹") +
                    "(" + DateTimeUtils.getFileSuffix() + BaseUtils.getRandomStr(3) + ").xlsx");
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }

        return HttpResult.ok(url);
    }

    @ApiOperation("批量删除包裹")
    @PostMapping("/batchDelete")
    public HttpResult<Boolean> batchDelete(@RequestBody PageBaseSearchDto dto) {
        if (ObjectUtils.isEmpty(dto.getIdList())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意记录"));
        }
        return HttpResult.ok(iShopPackService.remove(Wrappers.<ShopPack>lambdaQuery()
                .eq(ShopPack::getStatus, 1)
                .in(ShopPack::getId, dto.getIdList())));
    }

}
