package com.hzjm.tts.auth;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.hzjm.common.model.BaseException;
import com.hzjm.tts.auth.data.response.AuthShop;
import com.hzjm.tts.common.TTSApiRequestServiceImpl;
import com.hzjm.tts.product.data.AuthorizedStatus;
import com.hzjm.tts.product.data.BrandStatus;
import com.hzjm.tts.product.data.request.GetBrandsRequest;
import com.hzjm.tts.product.data.response.TTSBrand;
import com.hzjm.tts.product.service.ITTSProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TTSAuthShopBrandManager {

    private static final String TTS_BRAND_PREFIX = "knet:api:token:tts:brands:";
    private static final String ID_FIELD = "id";
    private static final String NAME_FIELD = "name";
    private static final String AUTH_STATUS_FIELD = "authorizedStatus";
    private static final String BRAND_STATUS_FIELD = "brandStatus";
    private static final String T1_BRAND_FIELD = "isT1Brand";

    @Autowired
    private TTSTokenGuardian tokenGuardian;

    private Map<String, TTSBrand> brandsMap = new HashMap<>();

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    @Autowired
    ITTSProductService productService;

    @PostConstruct
    void loadBrands() {
        loadTTSBrand();
        if (ObjectUtils.isEmpty(brandsMap)) {
            refreshBrands();
        }
    }

    public void refreshBrands() {
        try {
            List<TTSBrand> authorizedBrands = productService.getBrands(new GetBrandsRequest(), "<EMAIL>");

            brandsMap = authorizedBrands.stream()
                    .collect(Collectors.toMap(
                            brand -> brand.getName().replaceAll("[\\s-]", "").toUpperCase(), // Map key
                            brand -> brand,// Map value (the TTSBrand object itself)
                            (e, r) -> r // Handle duplicate keys by keeping the latest value
                    ));

            brandsMap.forEach((key, val) -> {
                saveTTSBrand(val);
            });

        }catch (Exception e) {
            log.error("try to refresh brands when init TTSAuthShopBrandsManager, error info: ", e);
        }
    }

    private void loadTTSBrand() {
        Set<String> keys = redisTemplate.keys(TTS_BRAND_PREFIX + "*");
        if (ObjectUtils.isNotEmpty(keys)) {
            for (String key : keys) {
                Map<Object, Object> map = redisTemplate.opsForHash().entries(key);
                TTSBrand brand = new TTSBrand(
                        (String) map.get(ID_FIELD),
                        AuthorizedStatus.createFrom((String) map.get(AUTH_STATUS_FIELD)),
                        BrandStatus.createFrom((String) map.get(BRAND_STATUS_FIELD)),
                        Boolean.parseBoolean((String) map.get(T1_BRAND_FIELD)),
                        (String) map.get(NAME_FIELD)
                );
                brandsMap.put(key, brand);
            }
        }
    }

    private void saveTTSBrand(TTSBrand brand) {
        Map<String, String> map = new HashMap<>();
        map.put(NAME_FIELD, brand.getName());
        map.put(ID_FIELD, brand.getId());
        map.put(AUTH_STATUS_FIELD, brand.getAuthorizedStatus().getRawValue());
        map.put(BRAND_STATUS_FIELD, brand.getBrandStatus().getRawValue());
        map.put(T1_BRAND_FIELD, String.valueOf(brand.isT1Brand()));
        redisTemplate.opsForHash().putAll(getAuthShopBrandsKey(brand.getName()), map);
    }

    public List<TTSBrand> getAuthorizedBrands() {
        return brandsMap.values().stream()
                .filter(brand -> brand.getAuthorizedStatus() == AuthorizedStatus.AUTHORIZED)
                .collect(Collectors.toList());
    }

    public String getTTSBrandId(String knetBrandName) {
        if (knetBrandName.isEmpty()) {
            throw new BaseException("Get Authorized Brands error, no available auth shop info to request.");
        }

        //输入的所有 牌子 都 去掉空格，并全部大写
        //String processedBrandName = knetBrandName.replaceAll("[\\s-]", "").toUpperCase();

        TTSBrand matchedBrand = brandsMap.get(getAuthShopBrandsKey(knetBrandName));

        if (ObjectUtils.isEmpty(matchedBrand)) {
            throw new BaseException("Not Authorized Brand Matched: " + knetBrandName);
        }

        return matchedBrand.getId();
    }

    /**
     *  redis 所有的 key 都将会使用 大写 所有品牌名的 形式来处理
     * @param brandName
     * @return
     */
    private String getAuthShopBrandsKey(String brandName) {
        String processedBrandName = brandName.replaceAll("[\\s-]", "").toUpperCase();
        return TTS_BRAND_PREFIX + processedBrandName;
    }

}
