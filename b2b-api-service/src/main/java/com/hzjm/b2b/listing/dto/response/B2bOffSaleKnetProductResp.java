package com.hzjm.b2b.listing.dto.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:55
 * @description: 创建knet 商品 返回体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class B2bOffSaleKnetProductResp implements Serializable {

    private static final long serialVersionUID = 3731438618531773988L;
    @ApiModelProperty(value = "操作成功记录", required = true)
    List<ProductDto> products;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductDto implements Serializable {

        private static final long serialVersionUID = -7516630866378708232L;
        @ApiModelProperty(value = "listingId")
        private String listingId;

        @ApiModelProperty(value = "操作是否成功", example = "true，false")
        private Boolean isSuccess;
    }
}
