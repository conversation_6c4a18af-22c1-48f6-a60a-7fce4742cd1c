package com.hzjm.b2b.listing.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:55
 * @description: 下架knet 商品 请求体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class B2bOffSaleKnetProductRequest implements Serializable {

    private static final long serialVersionUID = -2155891542079397780L;
    @ApiModelProperty(value = "products")
    List<ProductDto> products;

    @Data
    public static class ProductDto implements Serializable {
        private static final long serialVersionUID = 7088706828015351365L;
        @ApiModelProperty(value = "listingId", required = true)
        private String listingId;
    }

    public static B2bOffSaleKnetProductRequest fromListingIds(List<String> listingIds) {
        B2bOffSaleKnetProductRequest request = new B2bOffSaleKnetProductRequest();
        request.setProducts(
                listingIds.stream().map(listingId -> {
                    ProductDto productDto = new ProductDto();
                    productDto.setListingId(listingId);
                    return productDto;
                }).collect(Collectors.toList())
        );
        return request;
    }
}
