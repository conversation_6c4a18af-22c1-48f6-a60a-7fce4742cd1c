package com.hzjm.goat.product.dto.price;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AliasShoeCondition {

    PRODUCT_CONDITION_INVALID("PRODUCT_CONDITION_INVALID"),
    PRODUCT_CONDITION_NEW("PRODUCT_CONDITION_NEW"),
    PRODUCT_CONDITION_USED("PRODUCT_CONDITION_USED"),
    PRODUCT_CONDITION_NEW_WITH_DEFECTS("PRODUCT_CONDITION_NEW_WITH_DEFECTS");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }

}
