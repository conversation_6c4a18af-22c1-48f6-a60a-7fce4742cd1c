package com.hzjm.goat.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class GetOrdersQueryParameters {

    @ApiModelProperty("根据订单状态过滤,枚举值查看枚举定义")
    OrderStatusFilter filter;

    @ApiModelProperty("根据订单状态过滤,枚举值查看枚举定义")
    String sortBy = "update_time";

    @ApiModelProperty("当前页数")
    int page;

    @ApiModelProperty("每页包含数量, 默认是 25")
    int pageSize = 50;

    public GetOrdersQueryParameters() {}

}
