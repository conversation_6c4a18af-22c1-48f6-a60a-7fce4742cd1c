package com.hzjm.goat.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 *  goat 的 分表 的返回数据结构体
 * {
 *    "metadata": {
 *        "totalPages": 360,
 *        "prevPage": 29,
 *        "currentPage": 30,
 *        "nextPage": 31,
 *        "pageItemCount": 25,
 *        "itemsPerPageCount": 25,
 *        "totalCount": 8987
 *    },
 *    "products": []
 * }
 * @param <T>
 */

@Data
public class GoatPageResponse<T> {

    MetaData metadata;

    @JSONField(name = "products", alternateNames = {"orders"})
    List<T> data;

    @Data
    public static class MetaData {
        int totalPages;
        int currentPage;
        int pageItemCount;
        int itemsPerPageCount;
        @ApiModelProperty("这个值在有些接口里不一定存在！")
        int totalCount;
    }

}
