package com.hzjm.goat.common;

import com.hzjm.common.infrastructure.service.httprequest.ApiToken;
import com.hzjm.common.infrastructure.service.httprequest.TokenGuardian;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Service
public class GoatTokenGuardian extends TokenGuardian {

    static final ApiToken[] GOAT_API_TOKENS = {
            // 瑕疵 stv
            new ApiToken(
                    TOKEN_PRIORITY_LOW,
                    "<EMAIL>",
                    "<EMAIL>",
                    "nrAxz5SQozxuUmmSQLyM"
            ),

            // goat instant ship prod
            new ApiToken(
                    TOKEN_PRIORITY_HIGH,
                    "<EMAIL>",
                    "<EMAIL>",
                    "ap1FHytUQF39f4NFiBFb"
            ),

            // goat stv prod
            new ApiToken(
                    TOKEN_PRIORITY_HIGH,
                    "<EMAIL>",
                    "<EMAIL>",
                    "ByzstaLZ2E9nw_5Msyu4"
            ),

            // goat stv & instant ship dev
            new ApiToken(
                    TOKEN_PRIORITY_HIGH,
                    "<EMAIL>",
                    "<EMAIL>",
                    "zNUh8vPT86x5govu-7YR"
            )
    };

    @PostConstruct
    public void initializeGoatApiTokens() {
        //addTokens(GOAT_API_TOKENS);
    }

    /**
     * @return
     */
    @Override
    protected String getTokenGroupName() {
        return "goat";
    }

    /**
     * @param apiToken
     * @return
     */
    @Override
    protected boolean doRefreshAccessToken(ApiToken apiToken) {
        return true;
    }

    @Override
    public String getAccessTokenByEmail(String email) {
        return Arrays.stream(GOAT_API_TOKENS)
                .filter(e -> e.getEmail().equals(email.toLowerCase()))
                .findFirst()
                .map(ApiToken::getAccessToken)
                .orElse(null);
    }


    @Override
    public String getApiAccessToken() {
        int randomIndex = ThreadLocalRandom.current().nextInt(GOAT_API_TOKENS.length);
        ApiToken randomToken = GOAT_API_TOKENS[randomIndex];
        return randomToken.getAccessToken();
    }

}
