package com.hzjm.goat.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

@Getter
@AllArgsConstructor
public enum AliasTokens {

    ALIAS_ONE(1, "goatapi_2pj2Jkr0hS696HrTUXWx5xsAGeypX26cu3rGkMx"),
    ALIAS_TWO(2, "goatapi_1Hrv3lYkyq4pVVxnXjvKou15QRiu365IZ0h7Jkr"),
    ALIAS_THREE(3, "goatapi_1LfYhtD46nFNKhyufK0ccDcTqbaXwZbJY1kNNb9"),
    ALIAS_FOUR(4, "goatapi_1wm32YqHLD1EPqjaSEZ3o4UKQG3enXpRI3zYk9m");
    // ALIAS_FIVE(5, "goatapi_13qKYCi3OtFByJpjAdBdIpmS8eA0VxwXQ0D5lYO"),
    // ALIAS_SIX(6, "goatapi_rKpKPM6d6e007qZey3A17wZKFIRJGASh1Jmb6v");

    public final Integer tokenId;
    public final String rawValue;
    private static final String cachePreKey = "alias:token:id:";

    @Override
    public String toString() {
        return this.rawValue;
    }

    // 获取 tokenId 存储值
    public static String cachePreKey(Integer tokenId) {
        return cachePreKey + tokenId;
    }

    // 根据传入的 token id 返回 对应的枚举 如果么有 就返回null
    public static AliasTokens getNextTokenBy(Integer tokenId) {
        for (AliasTokens nextToken : AliasTokens.values()) {
            if (Objects.equals(nextToken.tokenId, tokenId)) {
                return nextToken;
            }
        }
        return null;
    }

}

