package com.hzjm.goat.listing.dto;

import com.hzjm.goat.product.dto.price.BoxCondition;
import com.hzjm.goat.product.dto.price.ShoeCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 *  搜寻 Goat 上已上架商品的参数信息
 * size
 * productTemplateId
 * shoeCondition
 * boxCondition
 * extTag (自定义标签)
 * withDefects (查询有瑕疵的商品)
 *
 */
@Data
@AllArgsConstructor
public class GetProductsQueryParameters {

    @ApiModelProperty("Sku, 此sku查询请去除 '-' 和空格来实现精准搜索")
    String sku;

    @ApiModelProperty("鞋码")
    String size;

    @ApiModelProperty("Goat 的商品模版Id")
    String productTemplateId;

    @ApiModelProperty("鞋子状态")
    ShoeCondition shoeCondition;

    @ApiModelProperty("鞋盒状态")
    BoxCondition boxCondition;

    @ApiModelProperty("自定义标签")
    String extTag;

    @ApiModelProperty("你可以通过查询 withDefects=true 获取所有瑕疵的商品信息。")
    boolean withDefects;

    @ApiModelProperty("建议使用此接口都加上saleStatus参数限制，否则会导致数据库压力增大，甚至访问超时。直接默认 ACTIVE")
    SaleStatus saleStatus;
            //= SaleStatus.ACTIVE;

    @ApiModelProperty("当前页数")
    int page = 1;

    @ApiModelProperty("每页包含数量, 默认是 25")
    int pageSize = 100;

    public GetProductsQueryParameters() {}

    public GetProductsQueryParameters(String sku) {
        this.sku = sku;
    }

    public GetProductsQueryParameters(SaleStatus saleStatus) {
        this.saleStatus = saleStatus;
    }
}
