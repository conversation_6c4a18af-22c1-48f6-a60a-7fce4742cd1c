package com.hzjm.goat.listing.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * {
 *   "products": [
 *     {"id": 123456789, "priceCents": 10000},
 *     {"id": 101010101, "priceCents": 20000}
 *   ]
 * }
 */

@Data
@AllArgsConstructor
public class BatchUpdatePriceBody {

    List<ProductUpdatePriceItem> products;

    @Data
    @AllArgsConstructor
    public static class ProductUpdatePriceItem {
        @ApiModelProperty("创建的寄售单 Id")
        int id;

        @ApiModelProperty("价格，实际价格 x 100 的 int 值， 例如 214.50 美刀， 你需要输入 21450")
        int priceCents;
    }
}
