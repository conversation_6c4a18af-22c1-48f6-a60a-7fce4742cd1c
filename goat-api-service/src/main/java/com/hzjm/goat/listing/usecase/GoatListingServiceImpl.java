package com.hzjm.goat.listing.usecase;

import com.alibaba.fastjson.JSONObject;
import com.hzjm.common.annotation.RateLimit;
import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.infrastructure.service.IApiRequestService;
import com.hzjm.goat.common.GoatEndPoint;
import com.hzjm.goat.common.GoatTokenGuardian;
import com.hzjm.goat.common.dto.GoatPageResponse;
import com.hzjm.goat.listing.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GoatListingServiceImpl implements IGoatListingService{

    @Qualifier("apiRequestServiceImpl")
    @Autowired
    IApiRequestService apiRequestService;

    @Autowired
    GoatTokenGuardian goatTokenGuardian;

    /**
     * 从Goat 获取所有上架的商品
     *
     * @param query 搜索参数
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public GoatPageResponse<GoatProduct> getListingProducts(GetProductsQueryParameters query, String accountEmail) {
        HttpRequest<GoatPageResponse<GoatProduct>> request = GoatEndPoint.getListingProducts(query, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request);
    }

    /**
     * 获取 在Goat 已经 Consigned 的 寄售商品列表
     *
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public List<GoatConsignedProduct> getConsignedProducts(String accountEmail) {
        HttpRequest<GoatConsignedProductsResponse> request = GoatEndPoint.getConsignedProducts(goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request).getProducts();
    }

    /**
     * 在 Goat 上创建寄售商品
     * 产品创建是异步进行的，接口并不会立即返回 product_id。通常商品将会于五分钟内创建好，到时候可以通过  Get Products 查询
     *
     * @param body 商品结构
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public JSONObject createProduct(CreateProductBody body, String accountEmail) {
        HttpRequest<JSONObject> request = GoatEndPoint.createProduct(body, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return  apiRequestService.doRequest(request);
    }

    /**
     * 在 Goat 上 查询 寄售的商品信息
     *
     * @param productId 寄售商品 ID
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public GoatProduct getProduct(String productId, String accountEmail) {
        HttpRequest<GoatProduct> request = GoatEndPoint.getProduct(productId, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request);
    }

    /**
     * 批量更新 Product 价格
     *
     * @param updatePriceBody
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public JSONObject batchUpdateProductPrice(BatchUpdatePriceBody updatePriceBody, String accountEmail) {
        HttpRequest<JSONObject> request = GoatEndPoint.batchUpdateProductPrice(updatePriceBody, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return  apiRequestService.doRequest(request);
    }

    /**
     * 上架
     *
     * @param productId
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public JSONObject activateProduct(String productId, String accountEmail) {
        HttpRequest<JSONObject> request = GoatEndPoint.activateProduct(productId, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request);
    }

    /**
     * 批量下架商品
     *
     * @param body 需要下架的商品Id 列表
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public JSONObject batchDeactivateProduct(BatchDeactivateProductBody body, String accountEmail) {
        HttpRequest<JSONObject> request = GoatEndPoint.batchDeactivateProduct(body, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request);
    }

    /**
     * 在Goat上取消所售商品
     *
     * @param productId 产品Id
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public JSONObject cancelProduct(String productId, String accountEmail) {
        HttpRequest<JSONObject> request = GoatEndPoint.cancelProduct(productId, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request);
    }

    /**
     * 从 Goat 删除 寄售的商品
     *
     * @param productId 寄售产品Id
     * @param accountEmail  对应的账户
     * @return
     */
    @Override
    @RateLimit(name = "GoatAPI", limit = 150)
    public JSONObject deleteProduct(String productId, String accountEmail) {
        HttpRequest<JSONObject> request = GoatEndPoint.deleteProduct(productId, goatTokenGuardian.getAccessTokenByEmail(accountEmail));
        return apiRequestService.doRequest(request);
    }

    /**
     * 获取所有的 goat listing 数据 ，此方法会自动翻页所有的listing 数据
     *
     * @param parameters   请求参数
     * @param accountEmail 账户邮箱地址
     * @return
     */
    @Override
    public List<GoatProduct> getAllListingFromGoat(GetProductsQueryParameters parameters, String accountEmail) {
        List<GoatProduct> allData = new ArrayList<>();
        int page = 1;
        while (true) {
            try {
                parameters.setPage(page);
                GoatPageResponse<GoatProduct> response = this.getListingProducts(parameters, accountEmail);
                allData.addAll(response.getData());

                if (page >= response.getMetadata().getTotalPages()) {
                    break;
                }
                page++;
            } catch (Exception e) {
                String errorInfo = "Error occurred while fetching listing data from Goat: " + e.getMessage();
                log.error(errorInfo);
                // If an error occurs, break the loop
                break;
            }
        }

        // Goat 可能会返回多于 带有 extTag 标签的数据，需要过滤一下无关listing
        if (ObjectUtils.isEmpty(parameters.getExtTag())) {
            return allData;
        }else {
            return allData
                    .stream()
                    .filter(a -> a.getExtTag().equals(parameters.getExtTag()))
                    .collect(Collectors.toList());
        }
    }
}
