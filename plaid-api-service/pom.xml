<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hzjm</groupId>
        <artifactId>ocean-flow</artifactId>
        <version>1.0</version>
    </parent>

    <artifactId>plaid-api-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.hzjm</groupId>
            <artifactId>common</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.plaid</groupId>
            <artifactId>plaid-java</artifactId>
            <version>28.0.0</version>
        </dependency>


    </dependencies>


</project>
