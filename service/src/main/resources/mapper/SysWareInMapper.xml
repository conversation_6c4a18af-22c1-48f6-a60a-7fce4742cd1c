<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysWareInMapper">

    <select id="selectWareIn" resultType="com.hzjm.service.model.VO.SysPackWareInVo">

        select t1.log_no , CONVERT_TZ(t2.gmt_create, 'UTC', 'America/New_York') as gmt_create, t3.`nickname` ,t2.batch_no
        from shop_pack t1
                 inner join sys_ware_in t2 on t1.in_id = t2.id and t2.del_flag = 0
                 inner join sys_user t3 on t2.create_by_id = t3.id and t3.del_flag = 0
        where t1.log_no = #{logNo}
          and t1.del_flag = 0
          and t1.status = 2

    </select>

</mapper>
