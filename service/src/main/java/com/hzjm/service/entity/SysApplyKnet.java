package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 申请加入knet
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysApplyKnet", description="申请加入knet")
public class SysApplyKnet extends Model<SysApplyKnet> implements Serializable {

    private static final long serialVersionUID=1L;


    @ApiModelProperty(value = "id")
        @TableId(value = "id", type = IdType.AUTO)
        public Integer id;

    @ApiModelProperty(value = "申请人")
    public String name;

    @ApiModelProperty(value = "申请人邮箱")
    public String email;

    @ApiModelProperty(value = "申请人电话")
    public String phone;

    @ApiModelProperty(value = "Instagram 标识")
    public String instagramHandle;

    @ApiModelProperty(value = "月交易量")
    public String monthlyVolume;

    @ApiModelProperty(value = "删除标识")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    public Date createTime;

    @ApiModelProperty(value = "ASK US")
    public String askUs;

    @ApiModelProperty(value = "备注")
    public String remark;

    @ApiModelProperty(value = "Y : 已通知，null 未通知，N 通知失败")
    public String hasNotified;


}
