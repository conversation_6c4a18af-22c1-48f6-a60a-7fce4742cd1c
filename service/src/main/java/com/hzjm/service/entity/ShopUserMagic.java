package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 商家的magic账号
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ShopUserMagic", description="商家的magic账号")
public class ShopUserMagic extends Model<ShopUserMagic> implements Serializable {

    private static final long serialVersionUID=1L;


        @TableId(value = "id", type = IdType.AUTO)
        public Integer id;

        @TableField(value = "gmt_create", fill = FieldFill.INSERT)
        public Date gmtCreate;

        @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
        public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "商家id")
    public Integer shopId;

    @ApiModelProperty(value = "商家标识")
    public String shopUid;

    @ApiModelProperty(value = "touch系统用户id")
    public String magicUserId;

    @ApiModelProperty(value = "touch系统用户名")
    public String magicUsername;


}
