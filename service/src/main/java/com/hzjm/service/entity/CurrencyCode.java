package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.util.ObjectUtils;

/**
 * 货币类型枚举
 */
public enum CurrencyCode {
    USD("USD", "美元"),
    EUR("EUR", "欧元"),
    GBP("GBP", "英镑"),
    JPY("JPY", "日元"),
    CNY("CNY", "人民币"),
    HKD("HKD", "港币"),
    CAD("CAD", "加拿大元"),
    AUD("AUD", "澳大利亚元"),
    SGD("SGD", "新加坡元"),
    CHF("CHF", "瑞士法郎");

    @EnumValue
    private final String code;

    private final String description;

    CurrencyCode(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static CurrencyCode getValByCode(String code) {

        if (ObjectUtils.isEmpty(code)) {
            return null;
        }
        for (CurrencyCode status : CurrencyCode.values()) {
            if (code.equalsIgnoreCase(status.getCode())) {
                return status;
            }
        }

        return null;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
