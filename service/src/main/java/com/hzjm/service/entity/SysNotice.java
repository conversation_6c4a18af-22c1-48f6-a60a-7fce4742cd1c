package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hzjm.service.model.DTO.NoticeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 公告
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysNotice", description="公告")
public class SysNotice extends Model<SysNotice> implements Serializable {

    private static final long serialVersionUID=1L;


        @TableId(value = "id", type = IdType.AUTO)
        public Integer id;

        @TableField(value = "gmt_create", fill = FieldFill.INSERT)
        public Date gmtCreate;

        @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
        public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    public String coverImg;

    public String title;

    public String content;

    @ApiModelProperty(value = "多个逗号隔开，1-管理端，4-仓库，5-商家")
    public String ports;

    @ApiModelProperty(value = "状态，1-启用，2-禁用")
    public Integer status;

    @ApiModelProperty(value = "类型")
    public NoticeType type;


}
