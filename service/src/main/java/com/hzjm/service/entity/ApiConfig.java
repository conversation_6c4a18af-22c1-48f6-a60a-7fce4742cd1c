package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.hzjm.common.utils.BaseResultView;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ApiConfig", description="")
public class ApiConfig extends Model<ApiConfig> implements Serializable {

    private static final long serialVersionUID=1L;

    // 列表视图
    public interface ApiConfigListView extends BaseResultView {}
    // 详情视图
    public interface ApiConfigInfoView extends ApiConfigListView{}

        @TableId(value = "id", type = IdType.AUTO)
        @JsonView(ApiConfigListView.class)
    public Integer id;

        @TableField(value = "gmt_create", fill = FieldFill.INSERT)
        @JsonView(ApiConfigListView.class)
    public Date gmtCreate;

        @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
        @JsonView(ApiConfigListView.class)
    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "应用名称")
    @JsonView(ApiConfigListView.class)
    public String name;

    @ApiModelProperty(value = "应用id")
    @JsonView(ApiConfigListView.class)
    public String appId;

    @ApiModelProperty(value = "应用密钥")
    @JsonView(ApiConfigListView.class)
    public String appSecret;




}
