package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 编号池（每天0点清除）
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysCodePool", description="编号池（每天0点清除）")
public class SysCodePool extends Model<SysCodePool> implements Serializable {

    private static final long serialVersionUID=1L;


        @TableId(value = "id", type = IdType.AUTO)
        public Integer id;

        @TableField(value = "gmt_create", fill = FieldFill.INSERT)
        public Date gmtCreate;

        @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
        public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "类型，1-oneId，2-批次编号，3-出库单号")
    public Integer type;

    @ApiModelProperty(value = "编号")
    public String code;

    @ApiModelProperty(value = "日期")
    public String dateStr;


}
