package com.hzjm.service.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 商品处理绑定关系
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SysProdDeal", description = "商品处理绑定关系")
public class SysProdDeal extends Model<SysProdDeal> implements Serializable {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    public Integer id;

    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    public Date gmtCreate;

    @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "事件类型，3-代发，4-转运，5-套现，6-寄卖，7-平台内转移")
    public Integer type;

    @ApiModelProperty(value = "商品id")
    public Integer prodId;

    @ApiModelProperty(value = "当时归属者id")
    public Integer shopId;

    @ApiModelProperty(value = "当时仓库id")
    public Integer wareId;

    @ApiModelProperty(value = "状态，1-处理中，2-已失效，3-已完成")
    public Integer status;

    // 3|4-sys_prod_transport, 5-sys_prod_cash, 6-固定为0, 7-sys_prod_transfer,
    @ApiModelProperty(value = "事件关联记录id")
    public Integer relationId;

    @ApiModelProperty(value = "实际报价/寄售价")
    public BigDecimal salePrice;

    @ApiModelProperty(value = "商家报价")
    public BigDecimal quotePrice;

    @ApiModelProperty(value = "在仓时间")
    public Integer wareDays;

    @ApiModelProperty(value = "仓储费用")
    public BigDecimal wareFee;

    @ApiModelProperty(value = "寄售平台id")
    public Integer thirdPlatId;

    @ApiModelProperty(value = "寄售平台单号")
    public String platOrderNo;

    @ApiModelProperty(value = "寄售到手价")
    public BigDecimal soldPrice;

    @ApiModelProperty("寄售平台到手价")
    public BigDecimal platSoldPrice;

    @ApiModelProperty(value = "商品pku")
    public String pku;

    @ApiModelProperty(value = "商品sku")
    public String sku;

    @ApiModelProperty(value = "三方寄售单id")
    public Integer saleId;

    @ApiModelProperty(value = "货源")
    public String supply;

    @ApiModelProperty(value = "成本价")
    public BigDecimal costPrice;

    @ApiModelProperty(value = "出库价格")
    public BigDecimal outPrice;

    @ApiModelProperty(value = "入库时间")
    public Date gmtIn;

    @ApiModelProperty(value = "出库时间")
    public Date gmtOut;

    @ApiModelProperty(value = "touch寄售状态，1-在售，2-已售，3-已结算，4-已关闭")
    public Integer platProdStatus;

//    @ApiModelProperty(value = "审批人id")
//    public Integer operatorId;


}
