package com.hzjm.service.utils.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.ObjectUtils;

import java.util.List;

public class PageUtils {

    /**
     * 物理分页
     *
     * @param current 页码
     * @param size    每页数据量
     * @param voList  结果集
     */
    public static IPage page(Integer current, Integer size, List voList) {
        int total = voList.size();
        IPage voPage = new Page();
        voPage.setTotal(total);
        if (!ObjectUtils.isEmpty(size) && !ObjectUtils.isEmpty(current)) {
            voPage.setSize(size);
            voPage.setCurrent(current <= 0 ? 1 : current);
            if (total == 0) {
                voPage.setPages(0);
                voPage.setRecords(voList);
            } else {
                if (ObjectUtils.isEmpty(size) || ObjectUtils.isEmpty(current)) {
                    voPage.setPages(1);
                    voPage.setRecords(voList);
                } else {
                    int page = total / size;
                    voPage.setPages(voPage.getTotal() % size == 0 ? page : page + 1);

                    int end = (current <= 1 ? 1 : current) * size;
                    end = end < total ? end : total;
                    int begin = (current - 1) * size;
                    begin = begin <= 0 ? 0 : (begin <= end ? begin : end);
                    voPage.setRecords(voList.subList(begin, end));
                }
            }
        } else {
            voPage.setRecords(voList);
        }
        return voPage;
    }
}
