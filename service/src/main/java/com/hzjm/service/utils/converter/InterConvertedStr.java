package com.hzjm.service.utils.converter;

/**
 * <AUTHOR>
 * @date 2025/1/3 11:42
 * @description: 数字转换成对应字符串工具
 */
public class InterConvertedStr {

    /**
     * 获取状态中文字符串
     *
     * @param status 状态
     * @return 状态中文字符串
     */
    public static String getStatusCnString(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "空闲";
            case 2:
                return "寄卖中";
            case 3:
                return "出库中";
            case 4:
                return "套现中";
            case 6:
                return "已出库";
            case 7:
                return "平台转移中";
            case 9:
                return "代发中";
            case 10:
                return "转运中";
            default:
                return "";
        }
    }

    /**
     * 获取检查结果中文字符串
     *
     * @param checkResult 审核结果
     * @return 审核结果中文字符串
     */
    public static String getCheckResultCnString(Integer checkResult) {
        if (checkResult == null) {
            return "";
        }
        switch (checkResult) {
            case 1:
                return "合格";
            case 2:
                return "鞋盒严重破损-S";
            case 3:
                return "球鞋有瑕疵";
            case 4:
                return "假货";
            case 5:
                return "鞋盒轻微破损-S";
            case 6:
                return "非原盒-S";
            case 7:
                return "无鞋盒盖-S";
            case 8:
                return "单只脚";
            default:
                return "";
        }
    }

    /**
     * 获取转发类型中文字符串
     *
     * @param type 类型
     * @return 类型中文字符串
     */
    public static String getTypeCnString(Integer type) {
        if (type == null) {
            return "";
        }
        switch (type) {
            case 1:
                return "SHIPPING";
            case 2:
                return "DROPOFF";
            case 3:
                return "RETURNED";
            default:
                return "";
        }
    }

    /**
     * 获取事件类型中文字符串
     *
     * @param eventType 事件类型
     * @return 事件类型中文字符串
     */
    public static String getEventTypeCnString(Integer eventType) {
        if (eventType == null) {
            return "";
        }
        switch (eventType) {
            case 3:
                return "代发";
            case 4:
                return "转运";
            case 5:
                return "套现";
            case 6:
                return "寄卖";
            case 7:
                return "平台内转移";
            default:
                return "";
        }
    }
}
