package com.hzjm.service.utils.common;

import com.alibaba.fastjson.JSONObject;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.HttpClient;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.geo.Distance;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * redis工具
 *
 * <AUTHOR>
 * <p>
 * 2017年12月20日
 */
@Slf4j
@Component
public class RedisUtils {
    /**
     * redis模板
     * tip:此处使用@Resource是为了获取RedisTemplate< String, Object>,否则会注入失败
     */
    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${spring.profiles.active}")
    private String active;

    //微信公众号
    @Value("${pay.wxJsPayId}")
    public String wxJsPayId;

    @Value("${pay.wxJsPaySecret}")
    public String wxJsPaySecret;

    //微信小程序
    @Value("${pay.wxSmallPayId}")
    public String wxSmallPayId;

    @Value("${pay.wxSmallPaySecret}")
    public String wxSmallPaySecret;

    @Autowired
    private DefaultKaptcha defaultKaptcha;

    private String applicationName = "ocean-flow";

//    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
//        this.redisTemplate = redisTemplate;
//    }

    /**
     * 向hash表中存入数据
     *
     * @param hash  hash表
     * @param key   键
     * @param value 值
     */
    public void putForHash(String hash, String key, String value) {
        redisTemplate.opsForHash().put(hash, key, value);
    }

    /**
     * 获取hash表中的hashKey对应的map值
     *
     * @return
     */
    public Map<Object, Object> getForHash(String hashKey) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(hashKey);
        if (entries == null)
            return new HashMap<>();
        return entries;
    }

    /**
     * 获取hash表(hashKey)中key对应的值
     *
     * @param hashKey
     * @param key
     * @return
     */
    public String getForHash(String hashKey, String key) {
        return (String) redisTemplate.opsForHash().get(hashKey, key);
    }

    /**
     * 删除hash表中的key对应的值
     *
     * @param hashKey
     * @param key
     */
    public void deleteForHash(String hashKey, String key) {
        redisTemplate.opsForHash().delete(hashKey, key);
    }

    /**
     * 向redis里存入string，并设置过期时间为2h
     *
     * @param key   键
     * @param value 值
     */
    public void putForValueAndTimeOut(String key, String value) {
        redisTemplate.opsForValue().set(key, value, 2, TimeUnit.HOURS);// 向redis里存入数据和设置缓存时间
    }

    /**
     * 向redis里存入string，并设置过期时间
     *
     * @param key     键
     * @param value   值
     * @param timeout 过期数值
     * @param unit    时间单位
     */
    public void putForValueAndTimeOut(String key, String value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);// 向redis里存入数据和设置缓存时间
    }

    public void putForValueAndTimeOut(String key, String value, long timeout) {
        redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);// 向redis里存入数据和设置缓存时间
    }

    /**
     * 向redis里存入string
     *
     * @param key   键
     * @param value 值
     */
    public void putForValue(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 获取key对应的值
     *
     * @param key
     * @return
     */
    public Object getForValue(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取key的存活时间，单位秒
     *
     * @param key
     * @return
     */
    public Long ttl(String key) {
        return redisTemplate.getExpire(key);
    }

    /**
     * 获取key的剩余存货时间，单位秒
     *
     * @return
     */
    public Long restTtl(String key, long timeout, TimeUnit unit) {
        Long already = redisTemplate.getExpire(key);
		/*
		if(unit == TimeUnit.SECONDS)
			timeout = timeout * 1;
		else if(unit == TimeUnit.MINUTES)
			timeout = timeout * 60;
		else if(unit == TimeUnit.HOURS)
			timeout = timeout * 60 * 60;
		else if(unit == TimeUnit.DAYS)
			timeout = timeout * 60 * 60 * 24;
		*/

        Long diff = timeout - already;
        if (diff <= 0)
            diff = 0l;
        return diff;
    }

    /**
     * 设置key的存活时间，单位秒
     *
     * @param key
     * @param timeout
     */
    public void expire(String key, int timeout) {
        redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
    }

    /**
     * 删除键
     *
     * @param key
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    public Point getGeo(String key) {
        List<Point> positions = redisTemplate.opsForGeo().position(applicationName, key);
        Point point = null;
        if (!ObjectUtils.isEmpty(positions)) {
            point = positions.get(0);
        }
        return point;
    }

    public void addGeo(String key, BigDecimal lng, BigDecimal lat) {
        redisTemplate.opsForGeo().add(applicationName, new Point(lng.doubleValue(), lat.doubleValue()), key);
    }

    public BigDecimal getDistinct(String key1, String key2) {
        Distance distance = redisTemplate.opsForGeo().distance(applicationName, key1, key2, RedisGeoCommands.DistanceUnit.KILOMETERS);
        return new BigDecimal(distance.getValue()).setScale(2, RoundingMode.HALF_EVEN);
    }

    public void deleteGeo(String key) {
        redisTemplate.opsForGeo().remove(applicationName, key);
    }

    public List<String> getRadiusKeyList(BigDecimal lng, BigDecimal lat, Double radius) {
        List<String> keys = new ArrayList<>();
        if (ObjectUtils.isEmpty(lng) || ObjectUtils.isEmpty(lat)) {
            return keys;
        }

        String key = "tmp_u_" + new Random().nextInt(10);
        addGeo(key, lng, lat);

        GeoResults<RedisGeoCommands.GeoLocation> geoResults = redisTemplate.opsForGeo().radius(applicationName, key, new Distance(radius, Metrics.KILOMETERS));
        geoResults.getContent().forEach(data -> {
            RedisGeoCommands.GeoLocation content = data.getContent();
            keys.add((String) content.getName());
        });
        return keys;
    }

    public Map<String, Point> getRadiusKeyMap(BigDecimal lng, BigDecimal lat, Double radius) {
        List<String> keys = getRadiusKeyList(lng, lat, radius);
        return getGeoMap(keys);
    }

    public Map<String, Point> getGeoMap(List<String> keys) {
        Map<String, Point> pointMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(keys)) {
            keys.forEach(key -> {
                pointMap.put(key, getGeo(key));
            });
        }
        return pointMap;
    }

    /**
     * 生成验证码并存储在redis中
     *
     */
    public String getCode(String phone) {
        return null;
//        String code = null;
//        // 对手机号进行解密
//        phone = EncryptUtil.decrypt(phone);
//        if (ObjectUtils.isEmpty(phone)) {
//            throw new BaseException(LanguageConfigService.i18nForMsg("手机号格式错误"));
//        }
//
//        if (ObjectUtils.isEmpty(active) || active.contains("dev")) {
//            code = "000000"; // 验证码
//        } else {
//            Random r = new Random();
//            code = String.format("%06d", r.nextInt(999999));
//
//            // 短信发送
//            AliyunSmsUtils.sendSmsByCode(phone, code);
//        }
//
//        String key = String.format(SysConstants.sms, phone);
//        putForValueAndTimeOut(key, code, 5 * 60);
//        return code;
    }

    /**
     * 校验验证码
     *
     * @param phone     待验证的手机号
     * @param validCode 输入的验证码
     */
    public void checkCode(String phone, String validCode) {
        if (ObjectUtils.isEmpty(validCode)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Please enter the verification code"));
        }
        // 校验验证码
        String smsKey = String.format(SysConstants.sms, phone);
        String code = (String) getForValue(smsKey);
        if (ObjectUtils.isEmpty(code)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Invalid Verifcation Code"));
        }
        if (!code.equals(validCode)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Verification code is wrong, please try again"));
        }
        delete(smsKey);
    }

    public void getCaptcha(HttpServletRequest req, HttpServletResponse resp) {
        try {
            resp.setHeader("Cache-Control", "no-store, no-cache");
            resp.setContentType("image/jpeg");
            String capText = defaultKaptcha.createText();

            String key = String.format(SysConstants.captcha, req.getSession().getId());
            putForValueAndTimeOut(key, capText, 5 * 60);

            BufferedImage bi = defaultKaptcha.createImage(capText);
            ServletOutputStream out = resp.getOutputStream();
            ImageIO.write(bi, "jpg", out);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验图形验证码
     *
     * @param validCode 输入的验证码
     */
    public void checkCaptcha(String validCode) {
        if (ObjectUtils.isEmpty(validCode)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Please enter the verification code"));
        }
        // 校验验证码
        HttpServletRequest req = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String key = String.format(SysConstants.captcha, req.getSession().getId());
        String code = (String) getForValue(key);
        if (ObjectUtils.isEmpty(code)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Invalid Verifcation Code"));
        }
        if (!code.equals(validCode)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Verification code is wrong, please try again"));
        }
        delete(key);
    }

    public String getJsToken() {
        String accessToken = (String) getForValue(SysConstants.mpToken);
        if (ObjectUtils.isEmpty(accessToken)) {
            if (ObjectUtils.isEmpty(wxJsPayId) || ObjectUtils.isEmpty(wxJsPaySecret)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("公众号配置未设置"));
            }

            String result = HttpClient.doGet("https://api.weixin.qq.com/cgi-bin/token?" +
                    "grant_type=client_credential&appid=" + wxJsPayId + "&secret=" + wxJsPaySecret, null);
            JSONObject tokenJson = JSONObject.parseObject(result);
            accessToken = tokenJson.getString("access_token");
            if (ObjectUtils.isEmpty(accessToken)) {
                log.error("获取jsapi access_token失败：" + tokenJson.getString("errmsg"));
                throw new BaseException(LanguageConfigService.i18nForMsg("授权失败"));
            }
            putForValueAndTimeOut(SysConstants.mpToken, accessToken, tokenJson.getLongValue("expires_in") / 2);
        }
        return accessToken;
    }

    public String getJsTicket() {
        String ticket = (String) getForValue(SysConstants.jsTicket);
        if (ObjectUtils.isEmpty(ticket)) {
            String result = HttpClient.doGet("https://api.weixin.qq.com/cgi-bin/ticket/getticket" +
                    "?access_token=" + getJsToken() + "&type=jsapi", null);
            JSONObject tokenJson = JSONObject.parseObject(result);
            ticket = tokenJson.getString("ticket");
            if (ObjectUtils.isEmpty(ticket)) {
                log.error("获取jsapi ticket失败：" + tokenJson.getString("errmsg"));
                throw new BaseException(LanguageConfigService.i18nForMsg("授权失败"));
            }
            putForValueAndTimeOut(SysConstants.jsTicket, ticket, tokenJson.getLongValue("expires_in") / 2);
        }
        return ticket;
    }

    public String getMpToken() {
        String token = (String) getForValue(SysConstants.jsToken);
        if (ObjectUtils.isEmpty(token)) {
            String tokenStr = HttpClient.doGet("https://api.weixin.qq.com/cgi-bin/token?" +
                    "grant_type=client_credential&appid=" + wxSmallPayId + "&secret=" + wxSmallPaySecret, null);
            JSONObject tokenJson = JSONObject.parseObject(tokenStr);
            token = tokenJson.getString("access_token");

            putForValueAndTimeOut(SysConstants.jsToken, token, (tokenJson.getLong("expires_in") / 60));
        }
        return token;
    }

    public void checkContent(String content) {
        JSONObject params = new JSONObject();
        params.put("content", content);

        String body = HttpClient.doPost("https://api.weixin.qq.com/wxa/msg_sec_check?access_token=" + getMpToken(), params.toJSONString(), null);
        JSONObject data = JSONObject.parseObject(body);
        if (data.getInteger("errcode") != 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("评论内容中含有违法违规内容"));
        }
    }

    /**
     *  设置数据增加
     * @param key
     * @param incrementCount 增加的数量
     * @param expireDate 过期时间， 单位默认为秒
     * @return
     */
    public long setForIncrement(String key, int incrementCount, Instant expireDate) {
        long count = redisTemplate.opsForValue().increment(key, incrementCount);
        redisTemplate.expireAt(key, expireDate);
        return count;
    }

    public void setWithExpireDuration(String key, int value, Duration timeout) {
        redisTemplate.opsForValue().set(key, value, timeout);
    }
}
