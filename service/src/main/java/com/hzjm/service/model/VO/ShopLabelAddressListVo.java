package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * label 收件信息-列表信息
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Data
public class ShopLabelAddressListVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    public Integer id;

    @ApiModelProperty(value = "批次编号")
    public String batchNo;

    @ApiModelProperty(value = "物流单号")
    public String trackingNo;

    @ApiModelProperty(value = "商家Id")
    public String shopId;

    @ApiModelProperty(value = "商家UID")
    public String shopUid;

    @ApiModelProperty(value = "收件公司姓名")
    public String companyName;

    @ApiModelProperty(value = "收件人姓名")
    public String name;

    @ApiModelProperty(value = "手机号-国家码")
    public String prePhone;

    @ApiModelProperty(value = "联系方式")
    public String phone;

    @ApiModelProperty(value = "国家")
    public String country;

    @ApiModelProperty(value = "省/州")
    public String province;

    @ApiModelProperty(value = "市")
    public String city;

    @ApiModelProperty(value = "区/街道")
    public String district;

    @ApiModelProperty(value = "详细地址")
    public String detail;

    @ApiModelProperty(value = "详细地址2")
    public String detail2;

    @ApiModelProperty(value = "邮编")
    public String zipCode;

    @ApiModelProperty(value = "身份证号码")
    public String idCardNumber;

    @ApiModelProperty(value = " -1 已删除, 0 未删除")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    public Date gmtCreate;

    @ApiModelProperty(value = "最近更新时间")
    public Date gmtModify;

    @ApiModelProperty(value = "创建人")
    public Integer createById;


}
