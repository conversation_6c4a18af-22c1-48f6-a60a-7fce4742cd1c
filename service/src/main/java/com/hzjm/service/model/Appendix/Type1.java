package com.hzjm.service.model.Appendix;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class Type1 {
    /*
    {
        "shelvesName": "A1-01",
            "batchNo": "SF000001",
            "pku": null,
            "type": 1,
            "gmtCreate": "2023-03-23 14:20:18",
            "sku": "66667777",
            "wareName": "A仓",
            "brand": null,
            "checkResult": 1,
            "spec": "42",
            "remarks": null,
            "imgList": [
        "https://jsrmddq.oss-cn-hangzhou.aliyuncs.com/images/1675150386090128.png"
        ]
    }*/


    @ApiModelProperty("事件类型，1-入库信息")
    public String type;

    @ApiModelProperty("事件发生时间")
    public String gmtCreate;

    @ApiModelProperty("入库单id")
    public Integer inId;

    @ApiModelProperty("查验批次")
    public String batchNo;

    @ApiModelProperty("oneId")
    public String oneId;

    @ApiModelProperty("sku")
    public String sku;

    @ApiModelProperty("pku")
    public String pku;

    @ApiModelProperty("品牌")
    public String brand;

    @ApiModelProperty("品名")
    public String remarks;

    @ApiModelProperty("尺码")
    public String spec;

    @ApiModelProperty("验货结果，1:合格;1:鞋盒严重破损-S;3:球鞋有瑕疵;4:假货;5:鞋盒轻微破损-S;6:非原盒-S;7:无鞋盒盖-S;8:单只脚;")
    public String checkResult;

    @ApiModelProperty("验货图片")
    public List<String> imgList;

    @ApiModelProperty("所在仓库")
    public String wareName;

    @ApiModelProperty("所在仓位")
    public String shelvesName;

}
