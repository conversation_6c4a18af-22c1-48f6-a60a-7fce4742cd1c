package com.hzjm.service.model.touch;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TouchProductResponse {

//    picture	string	商品图片URL
//    sizeOptions	array	商品可用尺码
//    sku	string	商品货号
//    name	string	商品名称
//    singleGender	string	男款/女款
//    值范围 [ 'men', 'women', 'youth', 'infant' ]

    @ApiModelProperty("商品图片URL")
    public String picture;

    @ApiModelProperty("商品可用尺码")
    public List<sizeOption> sizeOptions;

    @ApiModelProperty("商品货号")
    public String sku;

    @ApiModelProperty("商品名称")
    public String name;

    @ApiModelProperty("款式，'men', 'women', 'youth', 'infant'")
    public String singleGender;

    @ApiModelProperty(value = "gender")
    public String gender;

    @ApiModelProperty(value = "颜色")
    public String color;

    @ApiModelProperty(value = "型号")
    public String productType;

    @ApiModelProperty(value = "类别")
    public String productCategory;

    @ApiModelProperty("品牌")
    public String brand;


    @Data
    public static class sizeOption {

        @ApiModelProperty("")
        public String presentation;

        @ApiModelProperty("")
        public String value;

    }

}
