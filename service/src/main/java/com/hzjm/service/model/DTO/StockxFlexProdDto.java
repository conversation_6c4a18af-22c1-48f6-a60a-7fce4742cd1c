package com.hzjm.service.model.DTO;

import com.hzjm.service.model.enums.FlexProdStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class StockxFlexProdDto implements Serializable {

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty("商品生成的 ID")
    Integer id;

    @ApiModelProperty("用户 ID")
    Integer userId;

    @ApiModelProperty("stockX Variant ID")
    String variantId;

    @ApiModelProperty("stockX template ID")
    String templateId;

    @ApiModelProperty("所属预报的 ID")
    String flexPreId;

    @ApiModelProperty("商品 Sku")
    String sku;

    @ApiModelProperty("尺码")
    String size;

    @ApiModelProperty("prod 图片")
    String img;

    @ApiModelProperty("商品品名")
    String productName;

    @ApiModelProperty("商品品牌")
    String brand;

    @ApiModelProperty("商品 PKU")
    String pku;

    @ApiModelProperty("已经绑定的 One ID")
    String oneId;

    @ApiModelProperty("Flex 商品状态")
    FlexProdStatus flexProdStatus;
}
