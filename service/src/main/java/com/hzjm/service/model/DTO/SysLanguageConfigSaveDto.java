package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 多语言配置表-编辑/保存
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Data
public class SysLanguageConfigSaveDto implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "id")
    public Integer id;

    @ApiModelProperty(value = "中文文本")
    public String zhCn;

    @ApiModelProperty(value = "英文文本")
    public String enUs;

    @ApiModelProperty(value = "消息内容")
    public String msg;

    // 不存在逻辑删除字段时，生成删除标识
    @TableField(exist = false)
    @ApiModelProperty("0-正常，-1-删除")
    private Integer delFlag = 0;

}
