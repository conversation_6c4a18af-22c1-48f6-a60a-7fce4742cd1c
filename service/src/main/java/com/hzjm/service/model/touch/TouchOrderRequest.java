package com.hzjm.service.model.touch;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TouchOrderRequest {

    @ApiModelProperty("touch系统用户ID")
    public String touchUserId;

    @ApiModelProperty(value = "寄售平台单号", required = true)
    public String platOrderNo;

    @ApiModelProperty(value = "寄售平台")
    public String platName;

    @ApiModelProperty(value = "订单备注")
    public String note;

    @ApiModelProperty(value = "运单label")
    public String labelImg;

    @ApiModelProperty("运单号")
    public String trackingNumber;

    @ApiModelProperty(value = "[已弃用]erp系统商品ID", hidden = true)
    public String erpProductId;

    @ApiModelProperty(value = "[已弃用]erp系统商品ID集合", hidden = true)
    public List<String> erpProductIdList;

    @ApiModelProperty(value = "erp系统商品集合", required = true)
    public List<ErpProduct> erpProductList;

    @Data
    public static class ErpProduct {

        @ApiModelProperty("erp系统商品ID")
        public String erpProductId;

        @ApiModelProperty("寄售平台单号") //
        public String orderNo;

    }

}
