package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ShopUserPlatDto implements Serializable {
    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.AUTO)
    public Integer id;

    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    public Date gmtCreate;

    @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "商户id")
    public Integer shopId;

    @ApiModelProperty(value = "KNET服务费")
    public BigDecimal ocFee;

    @ApiModelProperty(value = "三方平台id")
    public Integer platId;

    @ApiModelProperty(value = "三方服务费")
    public BigDecimal serviceRate;

    @ApiModelProperty(value = "三方提现费")
    public BigDecimal drawRate;

    @ApiModelProperty(value = "用户运费")
    public BigDecimal shippingFee;  // 美元

    @ApiModelProperty(value = "技术费")
    public BigDecimal techFee;  // 美元
}
