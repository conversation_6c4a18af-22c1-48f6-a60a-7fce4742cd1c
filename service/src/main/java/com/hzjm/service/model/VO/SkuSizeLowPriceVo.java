package com.hzjm.service.model.VO;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "sku+size各个平台的最低价", description = "SkuSizeLowPrice")
public class SkuSizeLowPriceVo implements Serializable {

    private Integer id;
    private String sku;
    private String size;
    private String searchKey;
    private Integer stockxPrice;
    private Integer goatPrice;
    private Integer goatIsPrice;
    private Integer kcPrice;
    private Integer ebayPrice;
    private Integer poizonPrice;
    private Integer ttsPrice;
    private Integer ttsLowestPrice;
    private Integer ttsHighestPrice;

}
