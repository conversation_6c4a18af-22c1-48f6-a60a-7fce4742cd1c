package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

/**
 * 留言-列表查询
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
public class SysProdDealNotePageDto extends PageBaseSearchDto {

    @ApiModelProperty(value = "事件类型，3-代发，4-转运，5-套现，6-寄卖，7-平台内转移")
    public Integer type;

    @ApiModelProperty(value = "事件关联记录id")
    public Integer relationId;

}
