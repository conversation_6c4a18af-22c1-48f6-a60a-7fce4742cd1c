package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShopUserAllListVo {

    @TableId(value = "id", type = IdType.AUTO)
    public Integer id;

    @ApiModelProperty(value = "账号标识")
    public String uid;

    @ApiModelProperty(value = "用户账号")
    public String account;

    @ApiModelProperty(value = "昵称")
    public String nickname;

    @ApiModelProperty(value = "真实姓名")
    public String realname;

    @ApiModelProperty(value = "手机号")
    public String phone;

    @ApiModelProperty(value = "邮箱")
    public String email;

    @ApiModelProperty("用户等级")
    public Integer level;
}
