package com.hzjm.service.model.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SysProdCashQuotedDto {

    @ApiModelProperty("单子id")
    private Integer id;

    @ApiModelProperty("商品id")
    private Integer prodId;

    @ApiModelProperty("商品id")
    private List<Integer> prodIdList;

    @ApiModelProperty("报价")
    private BigDecimal price;

}
