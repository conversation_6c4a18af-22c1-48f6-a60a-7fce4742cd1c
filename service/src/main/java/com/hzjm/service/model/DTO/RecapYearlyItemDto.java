package com.hzjm.service.model.DTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/16 09:40
 * @description: 商户年度汇总-热门商品dto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "商户年度汇总-热门商品dto", description = "商户年度汇总-热门商品dto")
public class RecapYearlyItemDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "排序", example = "1")
    private Long sort;

    @ApiModelProperty(value = "sku", example = "shop123")
    private String sku;

    @ApiModelProperty(value = "商品图片", example = "shop123")
    private String imgUrl;
}
