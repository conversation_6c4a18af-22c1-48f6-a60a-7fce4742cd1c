package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

/**
 * 后管权限-列表查询
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Data
public class SysPermissionPageDto extends PageBaseSearchDto {

    @ApiModelProperty("根据id批量搜索")
    private List<Integer> idList;

}
