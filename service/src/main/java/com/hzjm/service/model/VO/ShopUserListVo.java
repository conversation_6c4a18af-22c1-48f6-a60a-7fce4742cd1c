package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商家-列表信息
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Data
public class ShopUserListVo implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "账号标识")
    public String uid;

    @ApiModelProperty(value = "引荐人账号标识")
    public String parentUid;

    @ApiModelProperty(value = "引荐人id")
    public Integer parentId;

    @ApiModelProperty(value = "用户账号")
    public String account;

    @ApiModelProperty(value = "昵称")
    public String nickname;

    @ApiModelProperty(value = "姓名")
    public String realname;

    @ApiModelProperty(value = "头像")
    public String headImg;

    @ApiModelProperty(value = "手机号")
    public String phone;

    @ApiModelProperty(value = "邮箱")
    public String email;

    @ApiModelProperty(value = "用户状态，1-正常，2-已禁用")
    public Integer status;

    @ApiModelProperty(value = "上次登录时间")
    public Date gmtLastLogin;

    @ApiModelProperty("OC服务费")
    public BigDecimal ocFee;

    @ApiModelProperty("用户等级")
    public Integer level;

    @ApiModelProperty("平台寄售信息")
    public List<ShopUserPlatListVo> platList;

    @ApiModelProperty("管理员是否拥有该商家的数据权限")
    public Boolean isOwn;

    public String touchUsername;

    public String magicUsername;

    @ApiModelProperty(value = "客户经理")
    public String customerManager;

    @ApiModelProperty(value = "客户经理Id：使用平台账号的id")
    public Integer customerManagerId;

    @ApiModelProperty(value = "国家")
    public String country;

}
