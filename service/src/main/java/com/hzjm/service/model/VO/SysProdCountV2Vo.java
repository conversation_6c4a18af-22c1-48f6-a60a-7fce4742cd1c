package com.hzjm.service.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class SysProdCountV2Vo {

    @ApiModelProperty("总库存")
    private Long totalInventoryNum;

    @ApiModelProperty("瑕疵库存")
    private Integer brokenNum;

    @ApiModelProperty("在仓库存")
    private Integer wareNum;

    @ApiModelProperty("已出库")
    private Integer outNum;

    @ApiModelProperty("全部成本")
    private BigDecimal allCost;

    @ApiModelProperty("在仓成本")
    private BigDecimal wareCost;

    @ApiModelProperty("已出库成本")
    private BigDecimal outCost;


}
