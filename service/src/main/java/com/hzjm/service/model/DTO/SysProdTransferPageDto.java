package com.hzjm.service.model.DTO;

import com.hzjm.common.utils.DateTimeUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/**
 * 平台内转移-列表查询
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Data
public class SysProdTransferPageDto extends PageBaseSearchDto {

    @ApiModelProperty("申请人")
    private String uid;

    @ApiModelProperty("出库单号")
    private String oddNo;

    @ApiModelProperty("审核状态，1-审核中，2-已取消，3-待支付，4-待出库，5-已出库")
    private Integer status;

    @ApiModelProperty(value = "处理时间类型，1-待处理，2-今日待处理，3-待出库，4-已完成")
    public Integer dealTime;

    @ApiModelProperty("新识别码")
    private String newShopUid;

    @ApiModelProperty("原识别码")
    private String oldShopUid;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outBeginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outEndTime;

    public Date dealOutEndTime() {
        if (!ObjectUtils.isEmpty(outEndTime)) {
            if (DateTimeUtils.format(DateTimeUtils.sdfTime, outEndTime).contains(" 00:00:00")) {
                outEndTime = new Date(outEndTime.getTime() + 24 * 60 * 60 * 1000l);
            }
        }
        return outEndTime;
    }
}
