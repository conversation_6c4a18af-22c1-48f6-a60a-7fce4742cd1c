package com.hzjm.service.model.DTO;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 提现-编辑/保存
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Data
public class SysWithdrawSaveDto implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "识别码")
    public String shopUid;

    @ApiModelProperty(value = "申请人")
    public String applyName;

    @ApiModelProperty(value = "提现申请金额")
    public BigDecimal amount;

    @ApiModelProperty(value = "提现申请手续费(需要扣除的金额)")
    public BigDecimal cashoutAmountFee;

    @ApiModelProperty(value = "1-银行汇款")
    public Integer drawType;

    @ApiModelProperty(value = "提现-账户名称")
    public String drawName;

    @ApiModelProperty(value = "提现-账号")
    public String drawAccount;

    @ApiModelProperty(value = "提现-开户行")
    public String drawBank;

    @ApiModelProperty(value = "备注")
    public String note;


}
