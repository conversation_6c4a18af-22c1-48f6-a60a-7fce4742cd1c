package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.hzjm.common.utils.DateTimeUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

/**
 * 入库单-列表查询
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
public class SysWareInPageDto extends PageBaseSearchDto {

    public Integer id;

    @ApiModelProperty(value = "仓库id")
    public Integer wareId;

    @ApiModelProperty(value = "仓库id")
    public List<Integer> wareIdList;

    @ApiModelProperty(value = "类型，1-SHOPPING，2-DROPOFF")
    public Integer type;

    @ApiModelProperty(value = "批次编号")
    public String batchNo;

    @ApiModelProperty(value = "批次状态，1-入库中，2-包裹入库，3-商品查验")
    public Integer status;


}
