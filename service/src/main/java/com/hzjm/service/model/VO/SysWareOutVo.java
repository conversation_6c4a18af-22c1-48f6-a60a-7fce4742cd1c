package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 出库单-详情
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Data
public class SysWareOutVo implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "出库单号")
    public String oddNo;

    @ApiModelProperty(value = "事件类型，3-代发，4-转运，6-寄卖，14-平台直接出库")
    public Integer type;

    @ApiModelProperty(value = "事件关联记录id")
    public Integer relationId;

    @ApiModelProperty(value = "仓库id")
    public Integer wareId;

    @ApiModelProperty(value = "状态，1-待出库，2-已生成批次，3-已取消，4-部分出库，5-完全出库")
    public Integer status;

    @ApiModelProperty(value = "归属人")
    public String shopUid;

    @ApiModelProperty(value = "商家姓名")
    public String shopName;

    @ApiModelProperty(value = "运单图片")
    public String labelImg;

    @ApiModelProperty(value = "收件人")
    public String receiveName;

    @ApiModelProperty(value = "联系方式")
    public String receivePhone;

    @ApiModelProperty(value = "收件地址")
    public String receiveAddress;

    @ApiModelProperty("商品信息")
    public List<SysProdDealListVo> prodList;

    @ApiModelProperty
    public String remark;


}
