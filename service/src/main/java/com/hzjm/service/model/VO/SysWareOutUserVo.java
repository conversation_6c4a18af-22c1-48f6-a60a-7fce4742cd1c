package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 出库单任务情况-详情
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Data
public class SysWareOutUserVo implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "出库单id")
    public Integer outId;

    @ApiModelProperty(value = "用户id")
    public Integer userId;

    @ApiModelProperty(value = "操作人id")
    public Integer createById;




}
