package com.hzjm.service.model.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SysProdDealOutPriceDto {

    @ApiModelProperty("处理记录id集合")
    public List<Integer> dealIdList;

    @ApiModelProperty("出库价格")
    public BigDecimal outPrice;

    @ApiModelProperty("成本价")
    public BigDecimal costPrice;

    @ApiModelProperty("货源")
    public String supply;

}
