package com.hzjm.service.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商家端钱包首页金额
 */
@Data
public class SysMoneyCountVo {

    @ApiModelProperty("总金额")
    public String blance;

    @ApiModelProperty("可提现金额")
    public String availableForWithdrawal;

    @ApiModelProperty("体现金额")
    public String totalWithdrawals;

    @ApiModelProperty("在途金额")
    public String pendingOwning;

    @ApiModelProperty("存款金额")
    public String totalDeposit;

    @ApiModelProperty("冻结金额")
    public String frozenDeposit;


}
