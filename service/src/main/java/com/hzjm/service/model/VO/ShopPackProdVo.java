package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.TableLogic;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 包裹货品-详情
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Data
public class ShopPackProdVo implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "包裹id")
    public Integer packId;

    @ApiModelProperty(value = "商家id")
    public Integer shopId;

    @ApiModelProperty(value = "商品图片")
    public String img;

    @ApiModelProperty(value = "品牌")
    public String brand;

    @ApiModelProperty(value = "品名")
    public String remarks;

    @ApiModelProperty(value = "sku")
    public String sku;

    @ApiModelProperty(value = "尺码")
    public String spec;

    @ApiModelProperty(value = "数量")
    public Integer num;

    @ApiModelProperty(value = "成本")
    public BigDecimal costPrice;

    @ApiModelProperty(value = "货源")
    public String supply;

    @ApiModelProperty(value = "退货原因")
    public String returnedReason;

}
