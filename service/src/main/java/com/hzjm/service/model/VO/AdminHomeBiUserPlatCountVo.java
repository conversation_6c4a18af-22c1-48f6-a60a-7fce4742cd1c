package com.hzjm.service.model.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 管理端首页BI - 用户 - 查询各个平台的统计
 */
@Data
@ApiModel(value = "管理端首页BI - 用户 - 查询各个平台的统计")
public class AdminHomeBiUserPlatCountVo implements Serializable {

    public static final long serialVersionUID = 1L;

    @ApiModelProperty("stockxCount 统计")
    public Long stockxCount;
    @ApiModelProperty("goatCount 统计")
    public Long goatCount;
    @ApiModelProperty("goat_isCount 统计")
    public Long goat_isCount;
    @ApiModelProperty("kcCount 统计")
    public Long kcCount;
    @ApiModelProperty("ebayCount 统计")
    public Long ebayCount;
    @ApiModelProperty("poizonCount 统计")
    public Long poizonCount;
    @ApiModelProperty("tiktokCount 统计")
    public Long tiktokCount;


}
