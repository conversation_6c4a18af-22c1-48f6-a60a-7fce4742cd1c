package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

/**
 * 商家的touch账号-列表查询
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
public class ShopUserTouchPageDto extends PageBaseSearchDto {

}
