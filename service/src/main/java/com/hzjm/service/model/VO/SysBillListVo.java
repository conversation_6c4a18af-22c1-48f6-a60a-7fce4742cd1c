package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.hzjm.common.constants.SysConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 平台交易流水-列表信息
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Data
public class SysBillListVo implements Serializable {

    private static final long serialVersionUID=1L;

    @ApiModelProperty(value = "主键")
    public Integer id;

    @ApiModelProperty(value = "是否删除")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    public Date gmtCreate;

    @ApiModelProperty(value = "修改时间")
    public Date gmtModify;

    @ApiModelProperty(value = "商户订单号")
    public String outTradeNo;

    @ApiModelProperty(value = "金额，单位元")
    public BigDecimal totalFee;

    @ApiModelProperty(value = "流水状态，1为交易中，2为已完成，3为已取消，4为已过期")
    public Integer status;

    @ApiModelProperty(value = "收支类型，1为收入，-1为支出")
    public Integer ieType;

    @ApiModelProperty(value = "流水类型，1-商家充值，2-商家提现，3-代发，4-转运，5-套现，6-寄卖，7-平台内转移")
    public Integer relationType;

    @ApiModelProperty(value = "关联ID,1-订单ID")
    public Integer relationId;

    @ApiModelProperty(value = "支付类型【内容较多，详见PayDto实体】")
    public Integer payType;

    @ApiModelProperty(value = "用户ID")
    public Integer userId;

    @ApiModelProperty(value = "附加参数")
    public String attach;

    @ApiModelProperty(value = "申请人")
    public String name;

    @ApiModelProperty(value = "备注")
    public String note;

    @ApiModelProperty(value = "流程单编号")
    public String oddNo;

    @ApiModelProperty(value = "出库编号")
    public String outNo;

    @ApiModelProperty(value = "三方寄售平台单号")
    public String platOrderNo;

    @ApiModelProperty(value = "三方寄售平台单号 TTS的母单号")
    public String platOrderNoTTS;

    @ApiModelProperty(value = "交易账号")
    public String billAccount;

    @ApiModelProperty(value = "商家标识")
    public String shopUid;

    @ApiModelProperty(value = "商家姓名")
    public String shopName;

    @ApiModelProperty(value = "交易后钱包余额")
    public BigDecimal newMoney;

    @ApiModelProperty(value = "管理端备注")
    public String remark;

    @ApiModelProperty(value = "寄卖服务费")
    public BigDecimal serviceFee = SysConstants.zero;

    @ApiModelProperty(value = "寄售平台名称")
    public String thirdPlatName;

    @ApiModelProperty(value = "寄售平台id")
    public Integer thirdPlatId;

    @ApiModelProperty(value = "银行卡识别码")
    public String routingNumber;

    @ApiModelProperty(value = "银行卡信息来源")
    public String accountInfoSupply;

    @ApiModelProperty(value = "账户货币")
    public String accountCurrency;

    @ApiModelProperty(value = "1-银行汇款 2-支付宝")
    public Integer drawType;

    @ApiModelProperty(value = "账户持有人类型")
    public String accountHolderType;



}
