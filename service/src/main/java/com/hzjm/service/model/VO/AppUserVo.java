package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.hzjm.common.utils.BaseResultView;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * 用户-详情
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Data
public class AppUserVo implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "用户昵称")
    public String nickname;

    @ApiModelProperty(value = "头像")
    public String headImg;

    @ApiModelProperty(value = "手机号")
    public String phone;

    @ApiModelProperty(value = "用户状态，1-正常，2-已禁用")
    public Integer status;




}
