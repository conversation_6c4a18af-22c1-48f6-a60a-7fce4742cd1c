package com.hzjm.service.model.DTO;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 公告用户关联表-编辑/保存
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
@Data
public class SysNoticeUserSaveDto implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;



    @TableLogic
    public Integer delFlag;

    public Integer noticeId;

    public Integer userId;

    public Integer userType;

    @ApiModelProperty(value = "0-未读，1-已读")
    public Integer readStatus;


}
