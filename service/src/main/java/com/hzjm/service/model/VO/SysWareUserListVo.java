package com.hzjm.service.model.VO;

import com.hzjm.service.entity.SysPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 仓库人员-列表信息
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Data
public class SysWareUserListVo implements Serializable {

    private static final long serialVersionUID=1L;

    public Integer id;
/*

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "仓库id")
    public Integer wareId;
*/

    @ApiModelProperty(value = "用户id")
    public Integer userId;

    @ApiModelProperty(value = "使用人")
    public String nickname;

    @ApiModelProperty(value = "账号")
    public String account;

    @ApiModelProperty(value = "权限集合")
    public List<SysPermission> permissionList;

}
