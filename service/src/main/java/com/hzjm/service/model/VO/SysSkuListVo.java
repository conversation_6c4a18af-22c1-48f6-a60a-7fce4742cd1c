package com.hzjm.service.model.VO;

import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * sku池-列表信息
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
public class SysSkuListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    public Integer id;

    public Date gmtCreate;

    public Date gmtModify;

    @ApiModelProperty(value = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @ApiModelProperty(value = "商品图片")
    public String img;

    @ApiModelProperty(value = "品牌")
    public String brand;

    @ApiModelProperty(value = "品名")
    public String remarks;

    @ApiModelProperty(value = "尺码")
    public String spec;

    @ApiModelProperty(value = "pku")
    public String pku;

    @ApiModelProperty(value = "sku")
    public String sku;

    @ApiModelProperty(value = "创建人id")
    public Integer createById;

    @ApiModelProperty(value = "创建人")
    public String createBy;

    @ApiModelProperty("平台回收价(预报）")
    private BigDecimal prePrice;

    @ApiModelProperty(value = "热榜排名")
    public Integer hotRankNum;

    @ApiModelProperty(value = "告警库存")
    public Integer warnNum;

    @ApiModelProperty(value = "类型")
    public String productType;

    @ApiModelProperty(value = "颜色")
    public String color;

    @ApiModelProperty(value = "性别")
    public String gender;
}
