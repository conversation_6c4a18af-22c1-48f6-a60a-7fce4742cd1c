package com.hzjm.service.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.service.entity.SysProdSale;
import com.hzjm.service.model.DTO.SysProdSalePageDto;
import com.hzjm.service.model.VO.SysProdSaleCountVo;
import com.hzjm.service.model.VO.SysProdSaleListVo;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 三方寄售单 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-06-07
 */
@Mapper
public interface SysProdSaleMapper extends BaseMapper<SysProdSale> {

    @Select("select * from sys_prod_sale where id = #{id}")
    SysProdSale selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from sys_prod_sale ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into sys_prod_sale(" +
            "id, gmt_create, gmt_modify, del_flag, shop_id, odd_no, status, plat_order_no, plat_name, note, reason, check_id" +
            ") values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#{item.id}, #{item.gmtCreate}, #{item.gmtModify}, #{item.delFlag}, #{item.shopId}, #{item.oddNo}, #{item.status}, #{item.platOrderNo}, #{item.platName}, #{item.note}, #{item.reason}, #{item.checkId}" +
            ")" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<SysProdSale> dataList);

    @Select({"select * from sys_prod_sale ${ew.customSqlSegment}"})
    List<SysProdSale> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from sys_prod_sale ${ew.customSqlSegment}"})
    IPage<SysProdSale> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);

    @Select({"" +
            "select distinct plat_name from sys_prod_sale " +
            ""})
    List<String> platList();

    List<Integer> selectProdIdPending();

    List<Integer> selectProdIdPendingToday();

    IPage<SysProdSaleListVo> selectSaleList(Page page, @Param("dto") SysProdSalePageDto dto);
    List<SysProdSaleListVo> selectSaleList(@Param("dto") SysProdSalePageDto dto);
    SysProdSaleCountVo selectSaleListCount(@Param("dto") SysProdSalePageDto dto);
}
