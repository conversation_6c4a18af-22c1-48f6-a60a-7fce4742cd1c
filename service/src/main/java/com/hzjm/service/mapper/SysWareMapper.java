package com.hzjm.service.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import com.hzjm.service.entity.SysWare;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 仓库 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Mapper
public interface SysWareMapper extends BaseMapper<SysWare> {

    @Select("select * from sys_ware where id = #{id}")
    SysWare selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from sys_ware ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into sys_ware(" +
            "id, gmt_create, gmt_modify, del_flag, name, free_days, price, address" +
            ") values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#{item.id}, #{item.gmtCreate}, #{item.gmtModify}, #{item.delFlag}, #{item.name}, #{item.freeDays}, #{item.price}, #{item.address}" +
            ")" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<SysWare> dataList);

    @Select({"select * from sys_ware ${ew.customSqlSegment}"})
    List<SysWare> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from sys_ware ${ew.customSqlSegment}"})
    IPage<SysWare> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);
}
