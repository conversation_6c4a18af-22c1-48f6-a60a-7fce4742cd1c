package com.hzjm.service.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.service.entity.SysProdTransport;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 转运&代发 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Mapper
public interface SysProdTransportMapper extends BaseMapper<SysProdTransport> {

    @Select("select * from sys_prod_transport where id = #{id}")
    SysProdTransport selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from sys_prod_transport ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into sys_prod_transport(" +
            "id, gmt_create, gmt_modify, del_flag, shop_id, odd_no, type, status, total_fee, ware_fee, delivery_fee, plat_fee, free_fee, gmt_pay, gmt_pay_valid, receive_name, receive_phone, receive_address, label_img" +
            ") values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#{item.id}, #{item.gmtCreate}, #{item.gmtModify}, #{item.delFlag}, #{item.shopId}, #{item.oddNo}, #{item.type}, #{item.status}, #{item.totalFee}, #{item.wareFee}, #{item.deliveryFee}, #{item.platFee}, #{item.freeFee}, #{item.gmtPay}, #{item.gmtPayValid}, #{item.receiveName}, #{item.receivePhone}, #{item.receiveAddress}, #{item.labelImg}" +
            ")" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<SysProdTransport> dataList);

    @Select({"select * from sys_prod_transport ${ew.customSqlSegment}"})
    List<SysProdTransport> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from sys_prod_transport ${ew.customSqlSegment}"})
    IPage<SysProdTransport> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);

    /**
     * 查询商家尚未支付的转运代发费用
     */
    BigDecimal selectFeeByShopId(@Param("shopId")Integer shopId);
}
