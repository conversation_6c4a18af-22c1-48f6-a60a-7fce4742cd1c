package com.hzjm.service.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.model.DTO.SysProdPageDto;
import com.hzjm.service.model.VO.SysProdCountV2Vo;
import com.hzjm.service.model.VO.SysProdGroupListVo;
import com.hzjm.service.model.VO.SysProdListVo;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品筛选 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Mapper
public interface SysProdSearchMapper extends BaseMapper<SysProdSearch> {

    @Select("select * from sys_prod_search where id = #{id}")
    SysProdSearch selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from sys_prod_search ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into sys_prod_search(" +
            "id, gmt_create, gmt_modify, del_flag, prod_id, deal_id, shop_id, " +
            "one_id, sku, pku, spec, brand, remarks, supply, cost_price, status, " +
            "ware_id, shelves_id, check_result, shop_uid, shop_name, " +
            "odd_no, odd_type, in_log_no, in_batch_no, gmt_in, gmt_pay, " +
            "third_plat_id, third_plat_name, plat_order_no, " +
            "out_no, gmt_out," +
            "search_type, in_log_no_related,check_remark" +
            ") values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#{item.id}, #{item.gmtCreate}, #{item.gmtModify}, #{item.delFlag}, #{item.prodId}, #{item.dealId}, #{item.shopId}, " +
            "#{item.oneId}, #{item.sku}, #{item.pku}, #{item.spec}, #{item.brand}, #{item.remarks}, #{item.supply}, #{item.costPrice}, #{item.status}, " +
            "#{item.wareId}, #{item.shelvesId}, #{item.checkResult}, #{item.shopUid}, #{item.shopName}, " +
            "#{item.oddNo}, #{item.oddType}, #{item.inLogNo}, #{item.inBatchNo}, #{item.gmtIn}, #{item.gmtPay}, " +
            "#{item.thirdPlatId}, #{item.thirdPlatName}, #{item.platOrderNo}, " +
            "#{item.outNo}, #{item.gmtOut}, " +
            "#{item.searchType},#{item.inLogNoRelated},#{item.checkRemark}" +
            ")" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<SysProdSearch> dataList);

    @Select({"select * from sys_prod_search ${ew.customSqlSegment}"})
    List<SysProdSearch> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from sys_prod_search ${ew.customSqlSegment}"})
    IPage<SysProdSearch> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);

    @Select({"select ifnull(sum(cost_price),0) from sys_prod_search ${ew.customSqlSegment}"})
    BigDecimal sumCost(@Param("ew") LambdaQueryWrapper<SysProdSearch> qw);

    /**
     * 通过oneId 获取日期最老的的入库时间
     */
    @Select("SELECT gmt_in from sys_prod_search where del_flag = 0 and one_id =#{oneId} order by gmt_in limit 1")
    Date selectOldGmtInByoneId(@Param("oneId") String oneId);


    // 库存列表查询
    IPage<SysProdListVo> selectSysProdSearchVOList(Page page, @Param("dto") SysProdPageDto dto);

    // 库存列表 总量，用于维护分页
    Long selectSysProdSearchVOListNum(@Param("dto") SysProdPageDto dto);

    List<SysProdListVo> selectSysProdSearchVOList(@Param("dto") SysProdPageDto dto);

    List<SysProdListVo> selectProductForWare(@Param("dto") SysProdPageDto dto);

    // 库存列表查询 分组查询 sku
    List<SysProdGroupListVo> selectSysProdSearchVOListGroupBySku(@Param("dto") SysProdPageDto dto);

    Long selectSysProdSearchVOListGroupBySkuCount(@Param("dto") SysProdPageDto dto);

    // 库存列表查询 分组查询 仓库 货架
    List<SysProdGroupListVo> selectSysProdSearchVOListGroupByWare(@Param("dto") SysProdPageDto dto);

    Long selectSysProdSearchVOListGroupByWareCount(@Param("dto") SysProdPageDto dto);

    List<SysProdListVo> selectSysProdSearchVOListGroupBySkuAndSize(@Param("dto") SysProdPageDto dto);

    List<SysProdListVo> selectGroupBySkuForProduct(@Param("dto") SysProdPageDto dto);

    SysProdCountV2Vo selectSearchCount(@Param("dto") SysProdPageDto dto);

    /**
     * 按品名分类获取商品列表(分页)
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<SysProdListVo> selectGroupBySkuForProductPage(Page<SysProdListVo> page, @Param("dto") SysProdPageDto dto);

    List<SysProdListVo> selectGroupBySkuForProductCount(@Param("dto") SysProdPageDto dto);
}
