package com.hzjm.service.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.*;
import com.hzjm.service.entity.SysProdEvent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 商品事件 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Mapper
public interface SysProdEventMapper extends BaseMapper<SysProdEvent> {

    @Select("select * from sys_prod_event where id = #{id}")
    SysProdEvent selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from sys_prod_event ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into sys_prod_event(" +
            "id, gmt_create, gmt_modify, del_flag, prod_id, type, description, relation_id, shop_id" +
            ") values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#{item.id}, #{item.gmtCreate}, #{item.gmtModify}, #{item.delFlag}, #{item.prodId}, #{item.type}, #{item.description}, #{item.relationId}, #{item.shopId}" +
            ")" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<SysProdEvent> dataList);

    @Select({"select * from sys_prod_event ${ew.customSqlSegment}"})
    List<SysProdEvent> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from sys_prod_event ${ew.customSqlSegment}"})
    IPage<SysProdEvent> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);

    @Update({"<script>" +
            "update sys_prod_event t " +
            "left join sys_prod_deal t1 on t.prod_id = t1.prod_id and t1.status = 1 " +
            "set t.relation_id = t1.id " +
            "where t.relation_id = 0 and t.prod_id in (" +
            "<foreach collection='prodIdList' item='prodId' separator=','>#{prodId}</foreach>" +
            ")" +
            "</script>"})
    Boolean relateSale(@Param("prodIdList") List<Integer> prodIdList);
}
