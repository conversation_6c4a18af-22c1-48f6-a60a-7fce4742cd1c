package com.hzjm.service.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.service.entity.ShopUserAddress;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 商家地址 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Mapper
public interface ShopUserAddressMapper extends BaseMapper<ShopUserAddress> {

    @Select("select * from shop_user_address where id = #{id}")
    ShopUserAddress selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from shop_user_address ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into shop_user_address(" +
            "id, gmt_create, gmt_modify, del_flag, shop_id, type, name, pre_phone, phone, country, province, city, district, detail, show_address, zip_code" +
            ",company_name,detail2) values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#{item.id}, #{item.gmtCreate}, #{item.gmtModify}, #{item.delFlag}, #{item.shopId}, #{item.type}, #{item.name}, #{item.prePhone}, #{item.phone}, #{item.country}, #{item.province}, #{item.city}, #{item.district}, #{item.detail}, #{item.showAddress}, #{item.zipCode}" +
            ", #{item.companyName}, #{item.detail2})" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<ShopUserAddress> dataList);

    @Select({"select * from shop_user_address ${ew.customSqlSegment}"})
    List<ShopUserAddress> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from shop_user_address ${ew.customSqlSegment}"})
    IPage<ShopUserAddress> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);

    // 清空商家的默认地址设置
    @Update({"update shop_user_address set address_flag = '' where del_flag = 0 and address_flag = 'default' and shop_id =  #{shopId}"})
    void updateAddressFlagByShopId(@Param("shopId") Integer shopId);
}
