package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysThirdPlat;
import com.hzjm.service.model.DTO.SysThirdPlatPageDto;
import com.hzjm.service.model.VO.SysThirdPlatListVo;
import com.hzjm.service.model.VO.SysThirdPlatVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 收费管理 服务类
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
public interface ISysThirdPlatService extends IService<SysThirdPlat> {

    SysThirdPlat getByIdWithoutLogic(Integer id);

    SysThirdPlatVo getDetail(Integer id);

    Boolean saveSysThirdPlat(SysThirdPlat dto);

    Boolean insertList(List<SysThirdPlat> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysThirdPlatListVo> searchList(SysThirdPlatPageDto dto);

    List<SysThirdPlat> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysThirdPlat> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
