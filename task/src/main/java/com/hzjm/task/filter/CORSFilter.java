package com.hzjm.task.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ObjectUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
public class CORSFilter implements Filter {

    List<String> whiteOrigins = Arrays.asList(
            "https://knetexp.com",
            "https://www.knetexp.com",
            "https://admin.knetexp.com",
            "https://wms.knetexp.com",
            "https://supply.knetexp.com",

            "https://knetgroup.com",
            "https://www.knetgroup.com",
            "https://admin.knetgroup.com",
            "https://wms.knetgroup.com",
            "https://supply.knetgroup.com",

            "https://knetgrp.com",
            "https://www.knetgrp.com",
            "https://admin.knetgrp.com",
            "https://wms.knetgrp.com",
            "https://supply.knetgrp.com",

            "https://dev-admin.knetgroup.com",
            "https://dev-wms.knetgroup.com",
            "https://dev-supply.knetgroup.com",

            "http://knet-admin.movoui.net",
            "http://knet-wms.movoui.net",
            "http://knet-supply.movoui.net",

            "http://knet-dev-admin.movoui.net",
            "http://knet-dev-wms.movoui.net",
            "http://knet-dev-supply.movoui.net",

            "http://192.168.6.5"
    );
    List<String> filePath = Arrays.asList(
            "/upload", "/import"
    );

    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse res,
                         FilterChain chain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        String originHeader = ((HttpServletRequest) req).getHeader("Origin");

        // 指定可通过的origin，白名单为空时不限制
        if (ObjectUtils.isEmpty(whiteOrigins) || whiteOrigins.contains(originHeader)) {
            response.setHeader("Access-Control-Allow-Origin", originHeader);
            response.setHeader("Access-Control-Allow-Methods",
                    "POST, GET, PUT, PATCH, HEAD, OPTIONS, DELETE");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers",
                    "x-requested-with,Content-Type,authorization,Authorization,token,timezone,requestId,lang,wareid,language");
            response.setHeader("Access-Control-Allow-Credentials", "true");

        }

//        chain.doFilter(req, res);

        HttpServletRequest request = ((HttpServletRequest) req);
        String path = request.getServletPath();
        // post请求需打印参数
        if ("POST".equals(request.getMethod()) && (path.indexOf("upload") == -1)) {
            // 替换request，解决request的inputStream只能read一次的问题
            BodyReaderHttpServletRequestWrapper myRequestWrapper = new BodyReaderHttpServletRequestWrapper(
                    (HttpServletRequest) req);
            myRequestWrapper.setAttribute("params", myRequestWrapper.getPostParams());
            chain.doFilter(myRequestWrapper, response);
        } else {
            chain.doFilter(req, response);
        }
    }

    @Override
    public void destroy() {

    }

}
