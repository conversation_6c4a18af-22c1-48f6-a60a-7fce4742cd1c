package com.hzjm.task.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.hzjm.common.infrastructure.HttpMethod;
import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.model.HttpResult;
import com.hzjm.crosslisting.enums.PendingAction;
import com.hzjm.crosslisting.listing.data.dto.NotifyKnetListingInfoDto;
import com.hzjm.crosslisting.listing.entity.ListingActionItem;
import com.hzjm.common.utils.ObjectConvertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class KnetListingEndpoint {

    @Value("${knet.system-api}")
    public String host;

    private static Map<String, String> defaultHeader() {
        HashMap<String, String> defaultHeaders = new HashMap<String, String>();
        defaultHeaders.put("Charset", "UTF-8");
        defaultHeaders.put("Content-Type", "application/json");
        return defaultHeaders;
    }

    public HttpRequest<HttpResult<List<ListingActionItem>>> getPendingCreateKnetListing(PendingAction pendingAction) {
        Map<String, String> hashMap = new HashMap<>();
        hashMap.put("shouldUpdating", "true");
        hashMap.put("pendingAction", pendingAction.toString());
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/get_listing_mission",
                HttpMethod.GET,
                null,
                hashMap,
                null,
                new TypeReference<HttpResult<List<ListingActionItem>>>() {}
                );
    }

    public HttpRequest<HttpResult<Boolean>> postPlatformListingPullResult(NotifyKnetListingInfoDto notifyListingInfo) {
        Map<String, Object> pullResultMap = ObjectConvertUtils.objectToMap(notifyListingInfo);
        String body = JSONObject.toJSONString(pullResultMap);
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/post_update_info",
                HttpMethod.POST,
                KnetListingEndpoint.defaultHeader(),
                null,
                body,
                new TypeReference<HttpResult<Boolean>>() {}
        );
    }

    public HttpRequest<HttpResult<List<String>>> syncCreatingKpl() {
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/sync_creating_kpl",
                HttpMethod.PUT,
                KnetListingEndpoint.defaultHeader(),
                null,
                null,
                new TypeReference<HttpResult<List<String>>>() {}
        );
    }

    public HttpRequest<HttpResult<List<String>>> syncUpdatingKpl() {
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/sync_updating_kpl",
                HttpMethod.PUT,
                KnetListingEndpoint.defaultHeader(),
                null,
                null,
                new TypeReference<HttpResult<List<String>>>() {}
        );
    }

    public HttpRequest<HttpResult<List<String>>> syncDeactivatingKpl() {
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/sync_deactivating_kpl",
                HttpMethod.PUT,
                KnetListingEndpoint.defaultHeader(),
                null,
                null,
                new TypeReference<HttpResult<List<String>>>() {}
        );
    }

    public HttpRequest<HttpResult<List<String>>> calibrateNotDeactivateKnetProductListing() {
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/calibrate_not_deactivate_kpl",
                HttpMethod.PUT,
                KnetListingEndpoint.defaultHeader(),
                null,
                null,
                new TypeReference<HttpResult<List<String>>>() {}
        );
    }

    public HttpRequest<HttpResult<String>> fixRepeatPlatformListings() {
        return new HttpRequest<>(
                host,
                "/knet_listing_pool/fix_repeat_platform_listing",
                HttpMethod.PUT,
                KnetListingEndpoint.defaultHeader(),
                null,
                null,
                new TypeReference<HttpResult<String>>() {}
        );
    }

}
