package com.hzjm.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.SysTask;
import com.hzjm.service.handler.TaskHandler;
import com.hzjm.service.mapper.SysTaskMapper;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.service.job.ISysTaskSaveService;
import com.hzjm.task.handler.TaskHandlerSelector;
import com.hzjm.task.service.IPendingTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/13 16:12
 * @description:
 */
@Slf4j
@Service
public class PendingTaskServiceImpl extends ServiceImpl<SysTaskMapper, SysTask> implements IPendingTaskService {
    @Resource
    ISysTaskSaveService iSysTaskSaveService;
    @Resource
    private TaskHandlerSelector taskHandlerSelector;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void processTask(Integer taskId) {
        SysTask sysTask = baseMapper.selectById(taskId);
        if (BeanUtil.isEmpty(sysTask)) {
            log.error("任务不存在，任务id：{}", taskId);
            return;
        }
        TaskHandler handler = taskHandlerSelector.selectHandler(sysTask.getType());
        if (BeanUtil.isEmpty(handler)) {
            log.error("未找到任务处理器，任务类型：{}", sysTask.getType());
            return;
        }
        if (BeanUtil.isNotEmpty(handler)) {
            if (iSysTaskSaveService.updateTaskStatus(taskId, SysTaskStatus.PROCESSING)) {
                handler.execute(sysTask);
            }
        }
    }
}
